import pandas as pd
import numpy as np
import time
import glob
from datetime import datetime
import os

# --- 保守策略配置 ---
CONFIG = {
    "backtest": {
        "initial_capital": 2000.0,  # 进一步增加初始资金
        "data_pattern": "Binance_BTCUSDT_*_minute.csv",
        "start_date": "2020-04-01",  # 避开3月暴跌
        "end_date": "2024-12-31",
    },
    "strategy": {
        "leverage": 3,            # 大幅降低杠杆
        "grid_count": 10,         # 减少网格数量
        
        # --- 自适应区间参数 ---
        "adaptive_range": {
            "enabled": True,
            "ema_period": 6 * 60,       # 6小时EMA
            "atr_period": 6 * 60,       # 6小时ATR
            "atr_multiplier": 1.0,      # 进一步缩小网格范围
            "repaint_threshold": 0.02   # 更频繁重绘
        },
        
        # --- 严格风险管理 ---
        "risk_management": {
            "max_position_ratio": 0.5,    # 最大仓位50%
            "stop_loss_ratio": 0.08,      # 8%止损
            "take_profit_ratio": 0.12,    # 12%止盈
            "max_consecutive_losses": 5,   # 连续亏损次数限制
            "daily_loss_limit": 0.05,     # 单日最大亏损5%
        }
    }
}


def load_multiple_data_files(pattern, start_date=None, end_date=None):
    """加载并合并多个CSV文件"""
    print(f"Looking for files matching pattern: {pattern}")
    
    files = glob.glob(pattern)
    if not files:
        print(f"No files found matching pattern: {pattern}")
        return None
    
    files.sort()
    print(f"Found {len(files)} files: {files}")
    
    all_data = []
    
    for file in files:
        print(f"Loading {file}...")
        try:
            df = pd.read_csv(file, skiprows=1)
            df.columns = df.columns.str.lower()
            
            required_base_cols = ['date', 'open', 'high', 'low', 'close']
            volume_col = 'volume usdt' if 'volume usdt' in df.columns else None
            
            missing_cols = [col for col in required_base_cols if col not in df.columns]
            if missing_cols:
                print(f"Warning: Missing columns in {file}: {missing_cols}")
                continue
            
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            
            cols_to_use = ['open', 'high', 'low', 'close']
            if volume_col:
                cols_to_use.append(volume_col)
                df.rename(columns={volume_col: 'volume'}, inplace=True)
            else:
                df['volume'] = 0
            
            df.rename(columns={
                'open': 'Open', 'high': 'High', 'low': 'Low',
                'close': 'Close', 'volume': 'Volume'
            }, inplace=True)
            
            df = df[['Open', 'High', 'Low', 'Close', 'Volume']]
            
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.dropna(inplace=True)
            all_data.append(df)
            print(f"  Loaded {len(df)} records from {file}")
            
        except Exception as e:
            print(f"Error loading {file}: {e}")
            continue
    
    if not all_data:
        print("No valid data loaded from any file")
        return None
    
    print("Merging all data...")
    combined_df = pd.concat(all_data, axis=0)
    combined_df.sort_index(inplace=True)
    combined_df = combined_df[~combined_df.index.duplicated(keep='first')]
    
    if start_date:
        combined_df = combined_df[combined_df.index >= start_date]
    if end_date:
        combined_df = combined_df[combined_df.index <= end_date]
    
    print(f"Final combined dataset: {len(combined_df)} records")
    print(f"Date range: {combined_df.index.min()} to {combined_df.index.max()}")
    
    return combined_df


class ConservativeGridBacktester:
    """
    保守的网格策略回测器，重点关注资本保护
    """
    def __init__(self, data, config):
        self.data = data.copy()
        self.config = config
        self.strategy_cfg = config['strategy']
        self.backtest_cfg = config['backtest']
        self.risk_cfg = config['strategy']['risk_management']
        self._calculate_indicators()
        
        # 状态变量
        self.balance = self.backtest_cfg['initial_capital']
        self.initial_balance = self.backtest_cfg['initial_capital']
        self.position_size = 0.0
        self.avg_entry_price = 0.0
        self.liquidation_price = 0.0
        self.total_profit = 0.0
        self.trades = []
        self.equity_curve = []
        self.daily_stats = []

        # 网格状态
        self.grid_orders = {}
        self.grid_center = 0
        self.grid_range_high = 0
        self.grid_range_low = 0
        
        # 风险控制状态
        self.consecutive_losses = 0
        self.daily_start_balance = self.balance
        self.last_date = None
        self.trading_paused = False
        self.pause_until = None
        
        # 统计变量
        self.total_trades = 0
        self.winning_trades = 0
        self.max_position = 0

    def _calculate_indicators(self):
        """预计算技术指标"""
        print("Calculating indicators (EMA, ATR)...")
        adaptive_cfg = self.strategy_cfg['adaptive_range']
        
        self.data['ema'] = self.data['Close'].ewm(span=adaptive_cfg['ema_period'], adjust=False).mean()
        
        high_low = self.data['High'] - self.data['Low']
        high_close = np.abs(self.data['High'] - self.data['Close'].shift())
        low_close = np.abs(self.data['Low'] - self.data['Close'].shift())
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        self.data['atr'] = tr.ewm(span=adaptive_cfg['atr_period'], adjust=False).mean()
        
        self.data['ema'] = self.data['ema'].bfill()
        self.data['atr'] = self.data['atr'].bfill()
        
        # 计算波动率指标用于风险控制
        self.data['volatility'] = self.data['Close'].pct_change().rolling(window=60).std() * np.sqrt(60)
        self.data['volatility'] = self.data['volatility'].bfill()
        
        print("Indicators calculated.")

    def _update_grid_range(self, current_price, ema, atr, volatility):
        """动态生成网格区间，考虑市场波动率"""
        adaptive_cfg = self.strategy_cfg['adaptive_range']
        
        # 根据波动率调整网格范围
        volatility_multiplier = min(2.0, max(0.5, 1.0 / (volatility + 0.1)))
        
        self.grid_center = ema
        grid_range_half = atr * adaptive_cfg['atr_multiplier'] * volatility_multiplier
        self.grid_range_high = self.grid_center + grid_range_half
        self.grid_range_low = self.grid_center - grid_range_half
        
        grid_levels = np.linspace(self.grid_range_low, self.grid_range_high, 
                                 self.strategy_cfg['grid_count'])
        
        self.grid_orders.clear()
        
        # 更保守的资金分配
        available_capital = self.balance * 0.8  # 只使用80%的资金
        max_position_value = available_capital * self.strategy_cfg['leverage'] * self.risk_cfg['max_position_ratio']
        notional_per_grid = max_position_value / self.strategy_cfg['grid_count']

        for price in grid_levels:
            if price < current_price:
                quantity = notional_per_grid / price
                self.grid_orders[price] = {
                    'type': 'buy',
                    'quantity': quantity,
                    'filled': False
                }

    def _calculate_liquidation_price(self):
        """计算爆仓价格"""
        if self.position_size <= 0:
            self.liquidation_price = 0
            return
        
        leverage = self.strategy_cfg['leverage']
        # 非常保守的爆仓价格计算
        self.liquidation_price = self.avg_entry_price * (1 - 0.8 / leverage)

    def _check_risk_management(self, current_price, current_date):
        """全面的风险管理检查"""
        # 检查是否需要重置每日统计
        if self.last_date is None or current_date.date() != self.last_date.date():
            self.daily_start_balance = self.balance
            self.last_date = current_date
        
        # 检查交易暂停
        if self.trading_paused and self.pause_until and current_date < self.pause_until:
            return False
        elif self.trading_paused and self.pause_until and current_date >= self.pause_until:
            self.trading_paused = False
            self.consecutive_losses = 0
            print(f"Trading resumed on {current_date.strftime('%Y-%m-%d')}")
        
        if self.position_size <= 0:
            return False
        
        # 计算当前盈亏
        pnl_ratio = (current_price - self.avg_entry_price) / self.avg_entry_price
        
        # 止损检查
        if pnl_ratio <= -self.risk_cfg['stop_loss_ratio']:
            print(f"Stop loss triggered at {current_price:.2f}, PnL: {pnl_ratio:.2%}")
            self.consecutive_losses += 1
            return True
            
        # 止盈检查
        if pnl_ratio >= self.risk_cfg['take_profit_ratio']:
            print(f"Take profit triggered at {current_price:.2f}, PnL: {pnl_ratio:.2%}")
            self.consecutive_losses = 0  # 重置连续亏损计数
            return True
        
        # 单日亏损限制
        daily_pnl_ratio = (self.balance - self.daily_start_balance) / self.daily_start_balance
        if daily_pnl_ratio <= -self.risk_cfg['daily_loss_limit']:
            print(f"Daily loss limit reached: {daily_pnl_ratio:.2%}")
            self.consecutive_losses += 1
            return True
        
        # 连续亏损限制
        if self.consecutive_losses >= self.risk_cfg['max_consecutive_losses']:
            print(f"Max consecutive losses reached: {self.consecutive_losses}")
            self.trading_paused = True
            self.pause_until = current_date + pd.Timedelta(days=1)  # 暂停1天
            return True
            
        return False

    def _close_all_positions(self, current_price, reason="Risk Management"):
        """平掉所有仓位"""
        if self.position_size <= 0:
            return
        
        pnl = (current_price - self.avg_entry_price) * self.position_size
        self.total_profit += pnl
        self.balance += pnl
        
        trade = {
            'date': self.current_timestamp,
            'type': 'close_all',
            'price': current_price,
            'quantity': self.position_size,
            'profit': pnl,
            'position_after': 0,
            'reason': reason
        }
        self.trades.append(trade)
        self.total_trades += 1
        if pnl > 0:
            self.winning_trades += 1
        
        self.position_size = 0
        self.avg_entry_price = 0
        self.grid_orders.clear()

    def _process_trades(self, current_time, low_price, high_price, current_price):
        """处理网格交易逻辑"""
        self.current_timestamp = current_time
        
        if self.trading_paused:
            return []
        
        trades_executed = []
        
        # 处理卖单成交
        sell_orders = [(price, order) for price, order in self.grid_orders.items() 
                      if order['type'] == 'sell' and not order['filled']]
        
        for sell_price, sell_order in sorted(sell_orders):
            if high_price >= sell_price and self.position_size > 0:
                sold_qty = min(sell_order['quantity'], self.position_size)
                if sold_qty <= 0:
                    continue
                
                profit = (sell_price - self.avg_entry_price) * sold_qty
                self.total_profit += profit
                self.balance += profit
                
                self.position_size -= sold_qty
                if self.position_size < 1e-8:
                    self.position_size = 0
                    self.avg_entry_price = 0
                
                sell_order['filled'] = True
                
                trade = {
                    'date': current_time,
                    'type': 'sell',
                    'price': sell_price,
                    'quantity': sold_qty,
                    'profit': profit,
                    'position_after': self.position_size
                }
                trades_executed.append(trade)
                self.trades.append(trade)
                self.total_trades += 1
                if profit > 0:
                    self.winning_trades += 1
                    self.consecutive_losses = 0  # 重置连续亏损
        
        # 处理买单成交
        buy_orders = [(price, order) for price, order in self.grid_orders.items() 
                     if order['type'] == 'buy' and not order['filled']]
        
        for buy_price, buy_order in sorted(buy_orders, reverse=True):
            if low_price <= buy_price:
                buy_qty = buy_order['quantity']
                
                if self.position_size > 0:
                    total_cost = self.avg_entry_price * self.position_size + buy_price * buy_qty
                    self.position_size += buy_qty
                    self.avg_entry_price = total_cost / self.position_size
                else:
                    self.position_size = buy_qty
                    self.avg_entry_price = buy_price
                
                self.max_position = max(self.max_position, self.position_size)
                buy_order['filled'] = True
                
                # 创建更保守的卖单
                grid_spacing = (self.grid_range_high - self.grid_range_low) / self.strategy_cfg['grid_count']
                sell_price = buy_price + grid_spacing * 2.0  # 更大的利润间距
                
                if sell_price <= self.grid_range_high * 1.2:
                    self.grid_orders[sell_price] = {
                        'type': 'sell',
                        'quantity': buy_qty,
                        'filled': False
                    }
                
                trade = {
                    'date': current_time,
                    'type': 'buy',
                    'price': buy_price,
                    'quantity': buy_qty,
                    'profit': 0,
                    'position_after': self.position_size
                }
                trades_executed.append(trade)
                self.trades.append(trade)
                self.total_trades += 1
        
        return trades_executed

    def run(self):
        """主回测循环"""
        start_time = time.time()
        print("Starting conservative backtest run...")
        
        total_bars = len(self.data)
        progress_interval = max(1, total_bars // 50)
        
        for i, (timestamp, row) in enumerate(self.data.iterrows()):
            current_price = row['Close']
            low_price = row['Low']
            high_price = row['High']
            ema = row['ema']
            atr = row['atr']
            volatility = row['volatility']

            if i % progress_interval == 0:
                progress = (i / total_bars) * 100
                print(f"\rProgress: {progress:.1f}% | Date: {timestamp.strftime('%Y-%m-%d')} | Price: {current_price:.2f} | Balance: {self.balance:.2f}", end="")

            # 1. 检查爆仓
            if self.position_size > 0 and low_price <= self.liquidation_price:
                print(f"\n--- LIQUIDATION EVENT on {timestamp.strftime('%Y-%m-%d %H:%M')} ---")
                self.balance = 0
                self.equity_curve.append(0)
                break

            # 2. 风险管理检查
            if self._check_risk_management(current_price, timestamp):
                self._close_all_positions(current_price)

            # 3. 网格重绘（只在空仓且未暂停时）
            should_repaint = False
            if not self.trading_paused:
                if not self.grid_orders:
                    should_repaint = True
                elif self.grid_center > 0:
                    deviation = abs(current_price - self.grid_center) / self.grid_center
                    if deviation > self.strategy_cfg['adaptive_range']['repaint_threshold']:
                        should_repaint = True

            if should_repaint and self.position_size == 0:
                self._update_grid_range(current_price, ema, atr, volatility)

            # 4. 处理交易
            self._process_trades(timestamp, low_price, high_price, current_price)

            # 5. 更新状态
            self._calculate_liquidation_price()
            
            unrealized_pnl = 0
            if self.position_size > 0:
                unrealized_pnl = (current_price - self.avg_entry_price) * self.position_size
            
            current_equity = self.balance + unrealized_pnl
            self.equity_curve.append(current_equity)

        print(f"\nBacktest completed in {time.time() - start_time:.2f} seconds.")
        self._generate_report()

    def _generate_report(self):
        """生成详细报告"""
        print("\n" + "="*70)
        print("           CONSERVATIVE GRID STRATEGY BACKTEST REPORT")
        print("="*70)
        
        if not self.equity_curve:
            print("No data to generate report.")
            return
        
        equity_series = pd.Series(self.equity_curve)
        final_equity = equity_series.iloc[-1] if len(equity_series) > 0 else self.initial_balance
        
        total_return = (final_equity / self.initial_balance - 1) * 100
        
        peak = equity_series.expanding(min_periods=1).max()
        drawdown = (equity_series - peak) / peak
        max_drawdown = drawdown.min() * 100 if len(drawdown) > 0 else 0
        
        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
        
        if len(self.data) > 0:
            days = (self.data.index[-1] - self.data.index[0]).days
            years = days / 365.25
            annual_return = ((final_equity / self.initial_balance) ** (1/years) - 1) * 100 if years > 0 else 0
        else:
            annual_return = 0
        
        # 计算夏普比率
        if len(self.equity_curve) > 1:
            returns = pd.Series(self.equity_curve).pct_change().dropna()
            sharpe_ratio = returns.mean() / returns.std() * np.sqrt(365*24*60) if returns.std() > 0 else 0
        else:
            sharpe_ratio = 0
        
        print(f"Initial Capital:      {self.initial_balance:,.2f} USDT")
        print(f"Final Equity:         {final_equity:,.2f} USDT")
        print(f"Net Profit:           {final_equity - self.initial_balance:,.2f} USDT")
        print(f"Total Return:         {total_return:.2f}%")
        print(f"Annual Return:        {annual_return:.2f}%")
        print(f"Max Drawdown:         {max_drawdown:.2f}%")
        print(f"Sharpe Ratio:         {sharpe_ratio:.2f}")
        print("-" * 70)
        print(f"Total Trades:         {self.total_trades}")
        print(f"Winning Trades:       {self.winning_trades}")
        print(f"Win Rate:             {win_rate:.2f}%")
        print(f"Grid Profit:          {self.total_profit:,.2f} USDT")
        print(f"Max Position Size:    {self.max_position:.4f} BTC")
        print(f"Leverage Used:        {self.strategy_cfg['leverage']}x")
        print(f"Grid Count:           {self.strategy_cfg['grid_count']}")
        print(f"Max Consecutive Loss: {self.consecutive_losses}")
        print("-" * 70)
        
        if final_equity <= 0:
            print("RESULT: STRATEGY WAS LIQUIDATED ❌")
        elif total_return > 10:
            print("RESULT: STRATEGY HIGHLY PROFITABLE ✅")
        elif total_return > 0:
            print("RESULT: STRATEGY PROFITABLE ✅")
        else:
            print("RESULT: STRATEGY SURVIVED BUT UNPROFITABLE ⚠️")
        
        # 保存结果
        if self.trades:
            trades_df = pd.DataFrame(self.trades)
            trades_df.to_csv('conservative_backtest_trades.csv', index=False)
            print(f"\nTrade details saved to: conservative_backtest_trades.csv")
        
        equity_df = pd.DataFrame({
            'timestamp': self.data.index[:len(self.equity_curve)],
            'equity': self.equity_curve
        })
        equity_df.to_csv('conservative_backtest_equity_curve.csv', index=False)
        print(f"Equity curve saved to: conservative_backtest_equity_curve.csv")


def run_conservative_backtest():
    """运行保守策略回测"""
    print("="*80)
    print("           CONSERVATIVE GRID TRADING STRATEGY BACKTEST")
    print("="*80)
    
    data_df = load_multiple_data_files(
        CONFIG['backtest']['data_pattern'],
        CONFIG['backtest']['start_date'],
        CONFIG['backtest']['end_date']
    )
    
    if data_df is None:
        print("Failed to load data. Please check your file paths and data format.")
        return
    
    backtester = ConservativeGridBacktester(data_df, CONFIG)
    backtester.run()
    
    return backtester


if __name__ == "__main__":
    result = run_conservative_backtest()