import math
import os
from tabulate import tabulate

def simulate_crypto_trading(
    initial_capital: float,
    leverage: int,
    btc_start_price: float,
    btc_target_price: float,
    price_step_percent: float,
    reinvest_ratio: float,
    min_price_protection: float
):
    """
    模拟BTC杠杆交易收益过程
    
    参数:
        initial_capital (float): 初始本金 (USDT)
        leverage (int): 杠杆倍数
        btc_start_price (float): BTC起始价格 (USDT)
        btc_target_price (float): BTC目标价格 (USDT)
        price_step_percent (float): 每次价格涨幅步长 (%)
        reinvest_ratio (float): 加仓比例 (%)
        min_price_protection (float): 最低价保护 (USDT)
    
    返回:
        List[Dict]: 每阶段的详细收益数据
    """
    # 参数校验
    if initial_capital <= 0:
        raise ValueError("初始本金必须大于0")
    if not (1 <= leverage <= 100):
        raise ValueError("杠杆倍数必须在1-100之间")
    if btc_target_price < btc_start_price:
        raise ValueError("目标价格必须大于等于起始价格")
    if price_step_percent <= 0:
        raise ValueError("价格涨幅步长必须大于0")
    if not (0 <= reinvest_ratio <= 100):
        raise ValueError("加仓比例必须在0-100之间")
    if min_price_protection > btc_start_price:
        raise ValueError("最低价保护不能高于起始价格")

    # 初始化数据
    data = []
    current_price = btc_start_price
    current_capital = initial_capital
    step = 0

    # 生成价格序列
    price_sequence = []
    while current_price <= btc_target_price:
        price_sequence.append(round(current_price, 2))
        current_price *= (1 + price_step_percent / 100)
    
    # 计算各阶段数据
    for price in price_sequence:
        if step == 0:
            # 初始阶段
            position_value = initial_capital * leverage
            profit = 0.0
            reinvest_amount = 0.0
            cumulative_profit = 0.0
            cumulative_return = 0.0
            liquidation_price = round(price * (1 - 1 / leverage), 2)
            current_leverage = leverage
            cumulative_price_change = 0.0
        else:
            # 计算本轮盈利
            profit = round(current_capital * (price_step_percent / 100) * leverage, 2)
            # 计算加仓金额
            reinvest_amount = round(profit * (reinvest_ratio / 100) * leverage, 2)
            # 更新本金
            current_capital += profit
            # 累计收益
            cumulative_profit = round(current_capital - initial_capital, 2)
            cumulative_return = round(cumulative_profit / initial_capital * 100, 2)
            # 持仓价值
            position_value = round(current_capital * leverage, 2)
            # 爆仓价
            liquidation_price = round(price * (1 - 1 / leverage), 2)
            # 当前杠杆
            current_leverage = leverage
            # 累计涨幅
            cumulative_price_change = round((price - btc_start_price) / btc_start_price * 100, 2)

        # 添加阶段数据
        data.append({
            "阶段": step,
            "BTC价格(USDT)": round(price, 2),
            "涨幅(%)": f"{price_step_percent:.2f}%" if step > 0 else "-",
            "累计涨幅(%)": f"{cumulative_price_change:.2f}%" if cumulative_price_change > 0 else "0.00%",
            "当前杠杆": current_leverage,
            "本轮盈利(USDT)": round(profit, 2),
            "本轮加仓金额(USDT)": round(reinvest_amount, 2),
            "持仓价值(USDT)": round(position_value, 2),
            "累计收益(USDT)": round(cumulative_profit, 2),
            "累计收益率(%)": f"{cumulative_return:.2f}%" if step > 0 else "0.00%",
            "爆仓价(USDT)": liquidation_price
        })
        step += 1

    return data

def save_to_csv(data, filename="btc_simulation_result.csv"):
    """将结果保存为CSV文件"""
    import csv
    if not data:
        return
    
    # 确保文件名唯一
    counter = 1
    while os.path.exists(filename):
        filename = f"btc_simulation_result_{counter}.csv"
        counter += 1

    with open(filename, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=data[0].keys())
        writer.writeheader()
        writer.writerows(data)
    print(f"\n数据已保存至文件: {filename}")

def main():
    # 示例参数
    try:
        result = simulate_crypto_trading(
            initial_capital=100,
            leverage=10,
            btc_start_price=107000,
            btc_target_price=0.2800,
            price_step_percent=0.5,
            reinvest_ratio=100,
            min_price_protection=0.1700
        )
        
        # 打印表格
        print(tabulate(result, headers="keys", tablefmt="grid"))
        
        # 保存CSV
        save_to_csv(result)
        
    except ValueError as e:
        print(f"参数错误: {e}")

if __name__ == "__main__":
    main()