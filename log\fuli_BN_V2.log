2025-07-24 22:55:35,811 - INFO - [main] - 启动 ARGM-V6.1 策略...
2025-07-24 22:55:35,811 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-24 22:55:35,818 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-24 22:55:35,818 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-24 22:55:35,820 - INFO - [setup] - 正在执行启动设置...
2025-07-24 22:55:35,820 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-24 22:55:35,820 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-24 22:55:35,827 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-24 22:55:35,911 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-24 22:55:35,912 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-24 22:55:36,418 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-24 22:55:36,418 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-24 22:55:36,756 - INFO - [test_connection] - 服务器时间获取成功: 1753368936710
2025-07-24 22:55:36,756 - INFO - [test_connection] - 正在加载市场数据...
2025-07-24 22:55:40,841 - INFO - [test_connection] - 市场数据加载成功
2025-07-24 22:55:40,841 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-24 22:55:40,842 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-24 22:55:42,096 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 189.23
2025-07-24 22:55:42,096 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-24 22:55:43,368 - INFO - [test_connection] - 余额获取成功，USDT余额: 1657.64037018
2025-07-24 22:55:43,369 - INFO - [test_connection] - API连接测试完成！
2025-07-24 22:55:43,369 - INFO - [setup] - 正在加载市场数据...
2025-07-24 22:55:44,322 - INFO - [close] - 正在关闭程序...
2025-07-24 22:55:44,569 - INFO - [close] - 程序已关闭。
2025-07-24 22:57:18,900 - INFO - [main] - 启动 ARGM-V6.1 策略...
2025-07-24 22:57:18,901 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-24 22:57:18,909 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-24 22:57:18,909 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-24 22:57:18,911 - INFO - [setup] - 正在执行启动设置...
2025-07-24 22:57:18,912 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-24 22:57:18,912 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-24 22:57:18,920 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-24 22:57:19,002 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-24 22:57:19,003 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-24 22:57:19,498 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-24 22:57:19,498 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-24 22:57:19,829 - INFO - [test_connection] - 服务器时间获取成功: 1753369039785
2025-07-24 22:57:19,829 - INFO - [test_connection] - 正在加载市场数据...
2025-07-24 22:57:23,850 - INFO - [test_connection] - 市场数据加载成功
2025-07-24 22:57:23,851 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-24 22:57:23,851 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-24 22:57:25,122 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 189.61
2025-07-24 22:57:25,122 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-24 22:57:26,389 - INFO - [test_connection] - 余额获取成功，USDT余额: 1657.64037018
2025-07-24 22:57:26,389 - INFO - [test_connection] - API连接测试完成！
2025-07-24 22:57:26,389 - INFO - [setup] - 正在加载市场数据...
2025-07-24 22:57:30,559 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-24 22:57:31,828 - INFO - [set_hedge_mode] - 当前已是双向持仓模式
2025-07-24 22:57:33,102 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-24 22:57:37,962 - ERROR - [_internal_state_sync] - 状态同步失败: float() argument must be a string or a real number, not 'NoneType'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1079, in _internal_state_sync
    self.best_bid_price = float(ticker['bid'])
                          ^^^^^^^^^^^^^^^^^^^^
TypeError: float() argument must be a string or a real number, not 'NoneType'
2025-07-24 22:57:37,963 - ERROR - [setup] - 初始化设置失败: float() argument must be a string or a real number, not 'NoneType'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 406, in setup
    await self.full_state_sync()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1100, in full_state_sync
    await self._internal_state_sync()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1079, in _internal_state_sync
    self.best_bid_price = float(ticker['bid'])
                          ^^^^^^^^^^^^^^^^^^^^
TypeError: float() argument must be a string or a real number, not 'NoneType'
2025-07-24 22:57:37,963 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: float() argument must be a string or a real number, not 'NoneType'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1281, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 442, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 406, in setup
    await self.full_state_sync()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1100, in full_state_sync
    await self._internal_state_sync()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1079, in _internal_state_sync
    self.best_bid_price = float(ticker['bid'])
                          ^^^^^^^^^^^^^^^^^^^^
TypeError: float() argument must be a string or a real number, not 'NoneType'
2025-07-24 22:57:37,963 - INFO - [close] - 正在关闭程序...
2025-07-24 22:57:37,964 - INFO - [cancel_all_open_orders] - 正在取消所有 0 个挂单...
2025-07-24 22:57:39,231 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-24 22:57:39,489 - INFO - [close] - 程序已关闭。
2025-07-24 22:57:39,490 - INFO - [main] - 程序已完全退出。
2025-07-24 22:58:41,456 - INFO - [main] - 启动 ARGM-V6.1 策略...
2025-07-24 22:58:41,456 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-24 22:58:41,464 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-24 22:58:41,464 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-24 22:58:41,466 - INFO - [setup] - 正在执行启动设置...
2025-07-24 22:58:41,466 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-24 22:58:41,466 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-24 22:58:41,474 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-24 22:58:41,556 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-24 22:58:41,558 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-24 22:58:42,042 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-24 22:58:42,043 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-24 22:58:42,380 - INFO - [test_connection] - 服务器时间获取成功: 1753369122336
2025-07-24 22:58:42,380 - INFO - [test_connection] - 正在加载市场数据...
2025-07-24 22:58:46,407 - INFO - [test_connection] - 市场数据加载成功
2025-07-24 22:58:46,408 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-24 22:58:46,408 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-24 22:58:47,666 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 189.88
2025-07-24 22:58:47,666 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-24 22:58:48,929 - INFO - [test_connection] - 余额获取成功，USDT余额: 1657.64037018
2025-07-24 22:58:48,931 - INFO - [test_connection] - API连接测试完成！
2025-07-24 22:58:48,931 - INFO - [setup] - 正在加载市场数据...
2025-07-24 22:58:53,071 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-24 22:58:54,340 - INFO - [set_hedge_mode] - 当前已是双向持仓模式
2025-07-24 22:58:55,600 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-24 22:59:00,473 - ERROR - [_internal_state_sync] - 状态同步失败: float() argument must be a string or a real number, not 'NoneType'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1079, in _internal_state_sync
    self.best_bid_price = float(ticker['bid'])
                          ^^^^^^^^^^^^^^^^^^^^
TypeError: float() argument must be a string or a real number, not 'NoneType'
2025-07-24 22:59:00,474 - ERROR - [setup] - 初始化设置失败: float() argument must be a string or a real number, not 'NoneType'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 406, in setup
    await self.full_state_sync()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1100, in full_state_sync
    await self._internal_state_sync()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1079, in _internal_state_sync
    self.best_bid_price = float(ticker['bid'])
                          ^^^^^^^^^^^^^^^^^^^^
TypeError: float() argument must be a string or a real number, not 'NoneType'
2025-07-24 22:59:00,475 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: float() argument must be a string or a real number, not 'NoneType'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1281, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 442, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 406, in setup
    await self.full_state_sync()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1100, in full_state_sync
    await self._internal_state_sync()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1079, in _internal_state_sync
    self.best_bid_price = float(ticker['bid'])
                          ^^^^^^^^^^^^^^^^^^^^
TypeError: float() argument must be a string or a real number, not 'NoneType'
2025-07-24 22:59:00,476 - INFO - [close] - 正在关闭程序...
2025-07-24 22:59:00,476 - INFO - [cancel_all_open_orders] - 正在取消所有 0 个挂单...
2025-07-24 22:59:01,735 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-24 22:59:01,998 - INFO - [close] - 程序已关闭。
2025-07-24 22:59:01,998 - INFO - [main] - 程序已完全退出。
2025-07-24 23:00:36,987 - INFO - [main] - 启动 ARGM-V6.1 策略...
2025-07-24 23:00:36,988 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-24 23:00:36,995 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-24 23:00:36,995 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-24 23:00:36,996 - INFO - [setup] - 正在执行启动设置...
2025-07-24 23:00:36,996 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-24 23:00:36,997 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-24 23:00:37,003 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-24 23:00:37,090 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-24 23:00:37,091 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-24 23:00:37,608 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-24 23:00:37,608 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-24 23:00:37,944 - INFO - [test_connection] - 服务器时间获取成功: 1753369237900
2025-07-24 23:00:37,944 - INFO - [test_connection] - 正在加载市场数据...
2025-07-24 23:00:41,979 - INFO - [test_connection] - 市场数据加载成功
2025-07-24 23:00:41,979 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-24 23:00:41,979 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-24 23:00:43,232 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 190.23
2025-07-24 23:00:43,232 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-24 23:00:44,512 - INFO - [test_connection] - 余额获取成功，USDT余额: 1657.64037018
2025-07-24 23:00:44,513 - INFO - [test_connection] - API连接测试完成！
2025-07-24 23:00:44,513 - INFO - [setup] - 正在加载市场数据...
2025-07-24 23:00:48,624 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-24 23:00:49,901 - INFO - [set_hedge_mode] - 当前已是双向持仓模式
2025-07-24 23:00:51,160 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-24 23:00:56,033 - WARNING - [_internal_state_sync] - 获取的Ticker数据不完整: {'symbol': 'SOL/USDT:USDT', 'timestamp': 1753369249380, 'datetime': '2025-07-24T15:00:49.380Z', 'high': 192.8, 'low': 179.2, 'bid': None, 'bidVolume': None, 'ask': None, 'askVolume': None, 'vwap': 186.5621, 'open': 191.58, 'close': 190.14, 'last': 190.14, 'previousClose': None, 'change': -1.44, 'percentage': -0.752, 'average': 190.86, 'baseVolume': 38889197.54, 'quoteVolume': 7255248642.2201, 'markPrice': None, 'indexPrice': None, 'info': {'symbol': 'SOLUSDT', 'priceChange': '-1.4400', 'priceChangePercent': '-0.752', 'weightedAvgPrice': '186.5621', 'lastPrice': '190.1400', 'lastQty': '341.60', 'openPrice': '191.5800', 'highPrice': '192.8000', 'lowPrice': '179.2000', 'volume': '38889197.54', 'quoteVolume': '7255248642.2201', 'openTime': '1753282800000', 'closeTime': '1753369249380', 'firstId': '2493809559', 'lastId': '2498310608', 'count': '4500657'}}，准备重试...
2025-07-24 23:01:00,696 - WARNING - [_internal_state_sync] - 获取的Ticker数据不完整: {'symbol': 'SOL/USDT:USDT', 'timestamp': 1753369255300, 'datetime': '2025-07-24T15:00:55.300Z', 'high': 192.8, 'low': 179.2, 'bid': None, 'bidVolume': None, 'ask': None, 'askVolume': None, 'vwap': 186.5629, 'open': 191.58, 'close': 190.2, 'last': 190.2, 'previousClose': None, 'change': -1.38, 'percentage': -0.72, 'average': 190.89, 'baseVolume': 38898028.05, 'quoteVolume': 7256928740.1477, 'markPrice': None, 'indexPrice': None, 'info': {'symbol': 'SOLUSDT', 'priceChange': '-1.3800', 'priceChangePercent': '-0.720', 'weightedAvgPrice': '186.5629', 'lastPrice': '190.2000', 'lastQty': '0.13', 'openPrice': '191.5800', 'highPrice': '192.8000', 'lowPrice': '179.2000', 'volume': '38898028.05', 'quoteVolume': '7256928740.1477', 'openTime': '1753282800000', 'closeTime': '1753369255300', 'firstId': '2493809559', 'lastId': '2498311563', 'count': '4501612'}}，准备重试...
2025-07-24 23:01:05,377 - WARNING - [_internal_state_sync] - 获取的Ticker数据不完整: {'symbol': 'SOL/USDT:USDT', 'timestamp': 1753369259339, 'datetime': '2025-07-24T15:00:59.339Z', 'high': 192.8, 'low': 179.2, 'bid': None, 'bidVolume': None, 'ask': None, 'askVolume': None, 'vwap': 186.563, 'open': 191.58, 'close': 190.27, 'last': 190.27, 'previousClose': None, 'change': -1.31, 'percentage': -0.684, 'average': 190.92, 'baseVolume': 38899494.03, 'quoteVolume': 7257207608.3542, 'markPrice': None, 'indexPrice': None, 'info': {'symbol': 'SOLUSDT', 'priceChange': '-1.3100', 'priceChangePercent': '-0.684', 'weightedAvgPrice': '186.5630', 'lastPrice': '190.2700', 'lastQty': '0.03', 'openPrice': '191.5800', 'highPrice': '192.8000', 'lowPrice': '179.2000', 'volume': '38899494.03', 'quoteVolume': '7257207608.3542', 'openTime': '1753282800000', 'closeTime': '1753369259339', 'firstId': '2493809559', 'lastId': '2498311934', 'count': '4501983'}}，准备重试...
2025-07-24 23:01:06,393 - ERROR - [setup] - 初始化设置失败: 状态同步在多次重试后彻底失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 406, in setup
    await self.full_state_sync()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1114, in full_state_sync
    await self._internal_state_sync()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1109, in _internal_state_sync
    raise Exception("状态同步在多次重试后彻底失败")
Exception: 状态同步在多次重试后彻底失败
2025-07-24 23:01:06,394 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: 状态同步在多次重试后彻底失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1295, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 442, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 406, in setup
    await self.full_state_sync()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1114, in full_state_sync
    await self._internal_state_sync()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V2.py", line 1109, in _internal_state_sync
    raise Exception("状态同步在多次重试后彻底失败")
Exception: 状态同步在多次重试后彻底失败
2025-07-24 23:01:06,395 - INFO - [close] - 正在关闭程序...
2025-07-24 23:01:06,395 - INFO - [cancel_all_open_orders] - 正在取消所有 0 个挂单...
2025-07-24 23:01:07,660 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-24 23:01:07,918 - INFO - [close] - 程序已关闭。
2025-07-24 23:01:07,918 - INFO - [main] - 程序已完全退出。
