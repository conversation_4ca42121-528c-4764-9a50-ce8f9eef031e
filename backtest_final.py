import pandas as pd
import numpy as np
import time
import glob
from datetime import datetime
import os

# --- 策略与回测配置 ---
CONFIG = {
    "backtest": {
        "initial_capital": 1000.0,  # 增加初始资金以降低爆仓风险
        "data_pattern": "Binance_BTCUSDT_*_minute.csv",
        "start_date": "2020-01-01",  # 可选：开始日期
        "end_date": "2024-12-31",    # 可选：结束日期
    },
    "strategy": {
        "leverage": 10,           # 降低杠杆以减少风险
        "grid_count": 20,         # 减少网格数量以提高每格利润
        
        # --- [核心] 自适应区间参数 ---
        "adaptive_range": {
            "enabled": True,
            "ema_period": 12 * 60,      # 缩短EMA周期以更快适应市场
            "atr_period": 12 * 60,      # 缩短ATR周期
            "atr_multiplier": 1.5,      # 降低ATR倍数以缩小网格范围
            "repaint_threshold": 0.03   # 降低重绘阈值以更频繁调整
        },
        
        # --- 风险管理参数 ---
        "risk_management": {
            "max_position_ratio": 0.8,    # 最大仓位比例
            "stop_loss_ratio": 0.15,      # 止损比例
            "take_profit_ratio": 0.20,    # 止盈比例
        }
    }
}


def load_multiple_data_files(pattern, start_date=None, end_date=None):
    """加载并合并多个CSV文件，支持不同的列名格式"""
    print(f"Looking for files matching pattern: {pattern}")
    
    files = glob.glob(pattern)
    if not files:
        print(f"No files found matching pattern: {pattern}")
        return None
    
    files.sort()
    print(f"Found {len(files)} files: {files}")
    
    all_data = []
    
    for file in files:
        print(f"Loading {file}...")
        try:
            # 跳过第一行（网站信息），从第二行开始读取
            df = pd.read_csv(file, skiprows=1)
            
            # 处理不同的列名格式
            # 2020-2023年格式: unix,date,symbol,open,high,low,close,Volume BTC,Volume USDT,tradecount
            # 2024-2025年格式: Unix,Date,Symbol,Open,High,Low,Close,Volume BTC,Volume USDT,tradecount
            
            # 统一列名为小写
            df.columns = df.columns.str.lower()
            
            # 检查必要的列
            required_base_cols = ['date', 'open', 'high', 'low', 'close']
            volume_col = 'volume usdt' if 'volume usdt' in df.columns else None
            
            missing_cols = [col for col in required_base_cols if col not in df.columns]
            if missing_cols:
                print(f"Warning: Missing columns in {file}: {missing_cols}")
                continue
            
            # 转换日期格式
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            
            # 选择需要的列
            cols_to_use = ['open', 'high', 'low', 'close']
            if volume_col:
                cols_to_use.append(volume_col)
                df.rename(columns={volume_col: 'volume'}, inplace=True)
            else:
                df['volume'] = 0  # 如果没有成交量数据，设为0
            
            # 重命名列以保持一致性
            df.rename(columns={
                'open': 'Open',
                'high': 'High', 
                'low': 'Low',
                'close': 'Close',
                'volume': 'Volume'
            }, inplace=True)
            
            df = df[['Open', 'High', 'Low', 'Close', 'Volume']]
            
            # 确保数据类型正确
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 删除无效数据
            df.dropna(inplace=True)
            
            all_data.append(df)
            print(f"  Loaded {len(df)} records from {file}")
            
        except Exception as e:
            print(f"Error loading {file}: {e}")
            continue
    
    if not all_data:
        print("No valid data loaded from any file")
        return None
    
    # 合并所有数据
    print("Merging all data...")
    combined_df = pd.concat(all_data, axis=0)
    combined_df.sort_index(inplace=True)
    
    # 去除重复的时间戳
    combined_df = combined_df[~combined_df.index.duplicated(keep='first')]
    
    # 应用日期过滤
    if start_date:
        combined_df = combined_df[combined_df.index >= start_date]
    if end_date:
        combined_df = combined_df[combined_df.index <= end_date]
    
    print(f"Final combined dataset: {len(combined_df)} records")
    print(f"Date range: {combined_df.index.min()} to {combined_df.index.max()}")
    
    return combined_df


class ImprovedGridBacktester:
    """
    改进的网格策略回测器，包含更好的风险管理
    """
    def __init__(self, data, config):
        self.data = data.copy()
        self.config = config
        self.strategy_cfg = config['strategy']
        self.backtest_cfg = config['backtest']
        self.risk_cfg = config['strategy']['risk_management']
        self._calculate_indicators()
        
        # 状态变量
        self.balance = self.backtest_cfg['initial_capital']
        self.initial_balance = self.backtest_cfg['initial_capital']
        self.position_size = 0.0
        self.avg_entry_price = 0.0
        self.liquidation_price = 0.0
        self.total_profit = 0.0
        self.trades = []
        self.equity_curve = []
        self.daily_stats = []

        # 网格状态
        self.grid_levels = []
        self.grid_orders = {}
        self.grid_center = 0
        self.grid_range_high = 0
        self.grid_range_low = 0
        
        # 统计变量
        self.total_trades = 0
        self.winning_trades = 0
        self.max_position = 0
        self.max_drawdown_hit = 0

    def _calculate_indicators(self):
        """预计算技术指标"""
        print("Calculating indicators (EMA, ATR)...")
        adaptive_cfg = self.strategy_cfg['adaptive_range']
        
        # EMA计算
        self.data['ema'] = self.data['Close'].ewm(span=adaptive_cfg['ema_period'], adjust=False).mean()
        
        # ATR计算
        high_low = self.data['High'] - self.data['Low']
        high_close = np.abs(self.data['High'] - self.data['Close'].shift())
        low_close = np.abs(self.data['Low'] - self.data['Close'].shift())
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        self.data['atr'] = tr.ewm(span=adaptive_cfg['atr_period'], adjust=False).mean()
        
        # 填充初始NaN值
        self.data['ema'] = self.data['ema'].bfill()
        self.data['atr'] = self.data['atr'].bfill()
        
        print("Indicators calculated.")

    def _update_grid_range(self, current_price, ema, atr):
        """根据当前市场情况，动态生成网格区间"""
        adaptive_cfg = self.strategy_cfg['adaptive_range']
        
        self.grid_center = ema
        grid_range_half = atr * adaptive_cfg['atr_multiplier']
        self.grid_range_high = self.grid_center + grid_range_half
        self.grid_range_low = self.grid_center - grid_range_half
        
        # 生成网格价格水平
        self.grid_levels = np.linspace(self.grid_range_low, self.grid_range_high, 
                                     self.strategy_cfg['grid_count'])
        
        # 清空旧订单
        self.grid_orders.clear()
        
        # 计算每个网格的投入资金（考虑风险管理）
        max_position_value = self.initial_balance * self.strategy_cfg['leverage'] * self.risk_cfg['max_position_ratio']
        notional_per_grid = max_position_value / self.strategy_cfg['grid_count']

        # 设置买单（在当前价格下方）
        for price in self.grid_levels:
            if price < current_price:
                quantity = notional_per_grid / price
                self.grid_orders[price] = {
                    'type': 'buy',
                    'quantity': quantity,
                    'filled': False
                }

    def _calculate_liquidation_price(self):
        """计算爆仓价格"""
        if self.position_size <= 0:
            self.liquidation_price = 0
            return
        
        leverage = self.strategy_cfg['leverage']
        # 更保守的爆仓价格计算
        self.liquidation_price = self.avg_entry_price * (1 - 0.9 / leverage)

    def _check_risk_management(self, current_price):
        """检查风险管理条件"""
        if self.position_size <= 0:
            return False
        
        # 计算当前盈亏比例
        pnl_ratio = (current_price - self.avg_entry_price) / self.avg_entry_price
        
        # 止损检查
        if pnl_ratio <= -self.risk_cfg['stop_loss_ratio']:
            print(f"Stop loss triggered at {current_price:.2f}, PnL: {pnl_ratio:.2%}")
            return True
            
        # 止盈检查
        if pnl_ratio >= self.risk_cfg['take_profit_ratio']:
            print(f"Take profit triggered at {current_price:.2f}, PnL: {pnl_ratio:.2%}")
            return True
            
        return False

    def _close_all_positions(self, current_price, reason="Risk Management"):
        """平掉所有仓位"""
        if self.position_size <= 0:
            return
        
        # 计算平仓盈亏
        pnl = (current_price - self.avg_entry_price) * self.position_size
        self.total_profit += pnl
        self.balance += pnl
        
        # 记录交易
        trade = {
            'date': self.current_timestamp,
            'type': 'close_all',
            'price': current_price,
            'quantity': self.position_size,
            'profit': pnl,
            'position_after': 0,
            'reason': reason
        }
        self.trades.append(trade)
        self.total_trades += 1
        if pnl > 0:
            self.winning_trades += 1
        
        # 重置仓位
        self.position_size = 0
        self.avg_entry_price = 0
        
        # 清空所有网格订单
        self.grid_orders.clear()

    def _process_trades(self, current_time, low_price, high_price, current_price):
        """处理网格交易逻辑"""
        self.current_timestamp = current_time
        trades_executed = []
        
        # 1. 处理卖单成交（价格上涨触发）
        sell_orders = [(price, order) for price, order in self.grid_orders.items() 
                      if order['type'] == 'sell' and not order['filled']]
        
        for sell_price, sell_order in sorted(sell_orders):
            if high_price >= sell_price and self.position_size > 0:
                # 卖单成交
                sold_qty = min(sell_order['quantity'], self.position_size)
                if sold_qty <= 0:
                    continue
                
                # 计算利润
                profit = (sell_price - self.avg_entry_price) * sold_qty
                self.total_profit += profit
                self.balance += profit
                
                # 更新仓位
                self.position_size -= sold_qty
                if self.position_size < 1e-8:
                    self.position_size = 0
                    self.avg_entry_price = 0
                
                # 标记订单已成交
                sell_order['filled'] = True
                
                # 记录交易
                trade = {
                    'date': current_time,
                    'type': 'sell',
                    'price': sell_price,
                    'quantity': sold_qty,
                    'profit': profit,
                    'position_after': self.position_size
                }
                trades_executed.append(trade)
                self.trades.append(trade)
                self.total_trades += 1
                if profit > 0:
                    self.winning_trades += 1
        
        # 2. 处理买单成交（价格下跌触发）
        buy_orders = [(price, order) for price, order in self.grid_orders.items() 
                     if order['type'] == 'buy' and not order['filled']]
        
        for buy_price, buy_order in sorted(buy_orders, reverse=True):
            if low_price <= buy_price:
                # 买单成交
                buy_qty = buy_order['quantity']
                
                # 更新仓位和平均成本
                if self.position_size > 0:
                    total_cost = self.avg_entry_price * self.position_size + buy_price * buy_qty
                    self.position_size += buy_qty
                    self.avg_entry_price = total_cost / self.position_size
                else:
                    self.position_size = buy_qty
                    self.avg_entry_price = buy_price
                
                # 更新最大仓位记录
                self.max_position = max(self.max_position, self.position_size)
                
                # 标记买单已成交
                buy_order['filled'] = True
                
                # 创建对应的卖单
                grid_spacing = (self.grid_range_high - self.grid_range_low) / self.strategy_cfg['grid_count']
                sell_price = buy_price + grid_spacing * 1.5  # 增加卖单间距以提高利润
                
                if sell_price <= self.grid_range_high * 1.1:  # 允许稍微超出范围
                    self.grid_orders[sell_price] = {
                        'type': 'sell',
                        'quantity': buy_qty,
                        'filled': False
                    }
                
                # 记录交易
                trade = {
                    'date': current_time,
                    'type': 'buy',
                    'price': buy_price,
                    'quantity': buy_qty,
                    'profit': 0,
                    'position_after': self.position_size
                }
                trades_executed.append(trade)
                self.trades.append(trade)
                self.total_trades += 1
        
        return trades_executed

    def run(self):
        """主回测循环"""
        start_time = time.time()
        print("Starting backtest run...")
        
        total_bars = len(self.data)
        progress_interval = max(1, total_bars // 50)  # 每2%显示进度
        
        for i, (timestamp, row) in enumerate(self.data.iterrows()):
            current_price = row['Close']
            low_price = row['Low']
            high_price = row['High']
            ema = row['ema']
            atr = row['atr']

            # 显示进度
            if i % progress_interval == 0:
                progress = (i / total_bars) * 100
                print(f"\rProgress: {progress:.1f}% | Date: {timestamp.strftime('%Y-%m-%d')} | Price: {current_price:.2f} | Position: {self.position_size:.4f}", end="")

            # 1. 检查爆仓
            if self.position_size > 0 and low_price <= self.liquidation_price:
                print(f"\n--- LIQUIDATION EVENT on {timestamp.strftime('%Y-%m-%d %H:%M')} ---")
                print(f"Price: {low_price:.2f}, Liquidation Price: {self.liquidation_price:.2f}")
                self.balance = 0
                self.equity_curve.append(0)
                break

            # 2. 检查风险管理
            if self._check_risk_management(current_price):
                self._close_all_positions(current_price)

            # 3. 检查是否需要重绘网格
            should_repaint = False
            if not self.grid_orders:  # 没有活跃订单
                should_repaint = True
            elif self.grid_center > 0:  # 价格偏离中枢太远
                deviation = abs(current_price - self.grid_center) / self.grid_center
                if deviation > self.strategy_cfg['adaptive_range']['repaint_threshold']:
                    should_repaint = True

            if should_repaint and self.position_size == 0:  # 只有空仓时才重绘
                self._update_grid_range(current_price, ema, atr)

            # 4. 处理交易
            self._process_trades(timestamp, low_price, high_price, current_price)

            # 5. 更新状态
            self._calculate_liquidation_price()
            
            # 计算当前权益
            unrealized_pnl = 0
            if self.position_size > 0:
                unrealized_pnl = (current_price - self.avg_entry_price) * self.position_size
            
            current_equity = self.balance + unrealized_pnl
            self.equity_curve.append(current_equity)
            
            # 记录每日统计
            if timestamp.hour == 0 and timestamp.minute == 0:  # 每天记录一次
                self.daily_stats.append({
                    'date': timestamp.date(),
                    'equity': current_equity,
                    'position_size': self.position_size,
                    'total_trades': self.total_trades
                })

        print(f"\nBacktest completed in {time.time() - start_time:.2f} seconds.")
        self._generate_report()

    def _generate_report(self):
        """生成详细的回测报告"""
        print("\n" + "="*60)
        print("           IMPROVED GRID STRATEGY BACKTEST REPORT")
        print("="*60)
        
        if not self.equity_curve:
            print("No data to generate report.")
            return
        
        equity_series = pd.Series(self.equity_curve)
        final_equity = equity_series.iloc[-1] if len(equity_series) > 0 else self.initial_balance
        
        # 基本统计
        total_return = (final_equity / self.initial_balance - 1) * 100
        
        # 最大回撤计算
        peak = equity_series.expanding(min_periods=1).max()
        drawdown = (equity_series - peak) / peak
        max_drawdown = drawdown.min() * 100 if len(drawdown) > 0 else 0
        
        # 胜率计算
        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
        
        # 年化收益率计算
        if len(self.data) > 0:
            days = (self.data.index[-1] - self.data.index[0]).days
            years = days / 365.25
            annual_return = ((final_equity / self.initial_balance) ** (1/years) - 1) * 100 if years > 0 else 0
        else:
            annual_return = 0
        
        print(f"Initial Capital:      {self.initial_balance:,.2f} USDT")
        print(f"Final Equity:         {final_equity:,.2f} USDT")
        print(f"Net Profit:           {final_equity - self.initial_balance:,.2f} USDT")
        print(f"Total Return:         {total_return:.2f}%")
        print(f"Annual Return:        {annual_return:.2f}%")
        print(f"Max Drawdown:         {max_drawdown:.2f}%")
        print("-" * 60)
        print(f"Total Trades:         {self.total_trades}")
        print(f"Winning Trades:       {self.winning_trades}")
        print(f"Win Rate:             {win_rate:.2f}%")
        print(f"Grid Profit:          {self.total_profit:,.2f} USDT")
        print(f"Max Position Size:    {self.max_position:.4f} BTC")
        print(f"Leverage Used:        {self.strategy_cfg['leverage']}x")
        print(f"Grid Count:           {self.strategy_cfg['grid_count']}")
        print("-" * 60)
        
        if final_equity <= 0:
            print("RESULT: STRATEGY WAS LIQUIDATED ❌")
        elif total_return > 0:
            print("RESULT: STRATEGY PROFITABLE ✅")
        else:
            print("RESULT: STRATEGY SURVIVED BUT UNPROFITABLE ⚠️")
        
        # 保存详细交易记录
        if self.trades:
            trades_df = pd.DataFrame(self.trades)
            trades_df.to_csv('improved_backtest_trades.csv', index=False)
            print(f"\nTrade details saved to: improved_backtest_trades.csv")
        
        # 保存权益曲线
        equity_df = pd.DataFrame({
            'timestamp': self.data.index[:len(self.equity_curve)],
            'equity': self.equity_curve
        })
        equity_df.to_csv('improved_backtest_equity_curve.csv', index=False)
        print(f"Equity curve saved to: improved_backtest_equity_curve.csv")


def run_improved_backtest():
    """运行改进的回测分析"""
    print("="*70)
    print("           IMPROVED GRID TRADING STRATEGY BACKTEST")
    print("="*70)
    
    # 加载数据
    data_df = load_multiple_data_files(
        CONFIG['backtest']['data_pattern'],
        CONFIG['backtest']['start_date'],
        CONFIG['backtest']['end_date']
    )
    
    if data_df is None:
        print("Failed to load data. Please check your file paths and data format.")
        return
    
    # 运行回测
    backtester = ImprovedGridBacktester(data_df, CONFIG)
    backtester.run()
    
    return backtester


if __name__ == "__main__":
    # 运行改进的回测
    result = run_improved_backtest()