2025-08-04 00:06:59 - INFO - [main:2255] - 启动 ARGM-V6.1 策略...
2025-08-04 00:06:59 - INFO - [_initialize_exchange:518] - 交易所实例创建成功
2025-08-04 00:06:59 - INFO - [__init__:431] - 已设置WebSocket URL: wss://fstream.binance.com
2025-08-04 00:06:59 - INFO - [setup:706] - 正在执行启动设置...
2025-08-04 00:06:59 - INFO - [test_connection:528] - 正在测试API连接... (尝试 1/5)
2025-08-04 00:06:59 - INFO - [test_connection:531] - 正在测试基础网络连接...
2025-08-04 00:06:59 - INFO - [test_connection:563] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-08-04 00:06:59 - INFO - [run:2109] - 📊 状态报告器已启动
2025-08-04 00:07:21 - ERROR - [test_connection:572] - 端点测试失败 https://fapi.binance.com/fapi/v1/ping: Cannot connect to host fapi.binance.com:443 ssl:default [None]
2025-08-04 00:07:21 - INFO - [test_connection:563] - 测试端点: https://fapi.binance.com/fapi/v1/time
2025-08-04 00:07:28 - INFO - [close:900] - 正在关闭程序...
2025-08-04 00:07:28 - INFO - [close:929] - 程序已关闭。
2025-08-04 00:09:29 - INFO - [main:2255] - 启动 ARGM-V6.1 策略...
2025-08-04 00:09:29 - INFO - [_initialize_exchange:518] - 交易所实例创建成功
2025-08-04 00:09:29 - INFO - [__init__:431] - 已设置WebSocket URL: wss://fstream.binance.com
2025-08-04 00:09:29 - INFO - [setup:706] - 正在执行启动设置...
2025-08-04 00:09:29 - INFO - [test_connection:528] - 正在测试API连接... (尝试 1/5)
2025-08-04 00:09:29 - INFO - [test_connection:531] - 正在测试基础网络连接...
2025-08-04 00:09:29 - INFO - [test_connection:563] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-08-04 00:09:29 - INFO - [run:2109] - 📊 状态报告器已启动
2025-08-04 00:09:29 - ERROR - [test_connection:572] - 端点测试失败 https://fapi.binance.com/fapi/v1/ping: Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:29 - INFO - [test_connection:563] - 测试端点: https://fapi.binance.com/fapi/v1/time
2025-08-04 00:09:29 - ERROR - [test_connection:572] - 端点测试失败 https://fapi.binance.com/fapi/v1/time: Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:29 - ERROR - [test_connection:691] - 连接测试失败 (尝试 1/5): Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:29 - ERROR - [test_connection:692] - 详细错误信息: ClientConnectorError: Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:29 - INFO - [test_connection:695] - 等待 3 秒后重试...
2025-08-04 00:09:32 - INFO - [test_connection:528] - 正在测试API连接... (尝试 2/5)
2025-08-04 00:09:32 - INFO - [test_connection:531] - 正在测试基础网络连接...
2025-08-04 00:09:32 - INFO - [test_connection:563] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-08-04 00:09:32 - ERROR - [test_connection:572] - 端点测试失败 https://fapi.binance.com/fapi/v1/ping: Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:32 - INFO - [test_connection:563] - 测试端点: https://fapi.binance.com/fapi/v1/time
2025-08-04 00:09:32 - ERROR - [test_connection:572] - 端点测试失败 https://fapi.binance.com/fapi/v1/time: Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:32 - ERROR - [test_connection:691] - 连接测试失败 (尝试 2/5): Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:32 - ERROR - [test_connection:692] - 详细错误信息: ClientConnectorError: Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:32 - INFO - [test_connection:695] - 等待 3 秒后重试...
2025-08-04 00:09:35 - INFO - [test_connection:528] - 正在测试API连接... (尝试 3/5)
2025-08-04 00:09:35 - INFO - [test_connection:531] - 正在测试基础网络连接...
2025-08-04 00:09:35 - INFO - [test_connection:563] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-08-04 00:09:35 - ERROR - [test_connection:572] - 端点测试失败 https://fapi.binance.com/fapi/v1/ping: Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:35 - INFO - [test_connection:563] - 测试端点: https://fapi.binance.com/fapi/v1/time
2025-08-04 00:09:35 - ERROR - [test_connection:572] - 端点测试失败 https://fapi.binance.com/fapi/v1/time: Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:35 - ERROR - [test_connection:691] - 连接测试失败 (尝试 3/5): Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:35 - ERROR - [test_connection:692] - 详细错误信息: ClientConnectorError: Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:35 - INFO - [test_connection:695] - 等待 3 秒后重试...
2025-08-04 00:09:38 - INFO - [test_connection:528] - 正在测试API连接... (尝试 4/5)
2025-08-04 00:09:38 - INFO - [test_connection:531] - 正在测试基础网络连接...
2025-08-04 00:09:38 - INFO - [test_connection:563] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-08-04 00:09:38 - ERROR - [test_connection:572] - 端点测试失败 https://fapi.binance.com/fapi/v1/ping: Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:38 - INFO - [test_connection:563] - 测试端点: https://fapi.binance.com/fapi/v1/time
2025-08-04 00:09:38 - ERROR - [test_connection:572] - 端点测试失败 https://fapi.binance.com/fapi/v1/time: Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:38 - ERROR - [test_connection:691] - 连接测试失败 (尝试 4/5): Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:38 - ERROR - [test_connection:692] - 详细错误信息: ClientConnectorError: Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:38 - INFO - [test_connection:695] - 等待 3 秒后重试...
2025-08-04 00:09:41 - INFO - [test_connection:528] - 正在测试API连接... (尝试 5/5)
2025-08-04 00:09:41 - INFO - [test_connection:531] - 正在测试基础网络连接...
2025-08-04 00:09:41 - INFO - [test_connection:563] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-08-04 00:09:41 - ERROR - [test_connection:572] - 端点测试失败 https://fapi.binance.com/fapi/v1/ping: Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:41 - INFO - [test_connection:563] - 测试端点: https://fapi.binance.com/fapi/v1/time
2025-08-04 00:09:41 - ERROR - [test_connection:572] - 端点测试失败 https://fapi.binance.com/fapi/v1/time: Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:41 - ERROR - [test_connection:691] - 连接测试失败 (尝试 5/5): Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:41 - ERROR - [test_connection:692] - 详细错误信息: ClientConnectorError: Cannot connect to host fapi.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]
2025-08-04 00:09:41 - ERROR - [test_connection:698] - 所有连接测试尝试均失败
2025-08-04 00:09:41 - ERROR - [setup:782] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3_cloud.py", line 709, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-08-04 00:09:41 - CRITICAL - [main:2268] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3_cloud.py", line 2263, in main
    await asyncio.gather(main_task, reporter_task)
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3_cloud.py", line 795, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3_cloud.py", line 709, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-08-04 00:09:41 - INFO - [close:900] - 正在关闭程序...
2025-08-04 00:09:42 - INFO - [close:929] - 程序已关闭。
2025-08-04 00:09:42 - INFO - [main:2272] - 程序已完全退出。
2025-08-04 00:10:07 - INFO - [main:2255] - 启动 ARGM-V6.1 策略...
2025-08-04 00:10:07 - INFO - [_initialize_exchange:515] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-08-04 00:10:07 - INFO - [_initialize_exchange:518] - 交易所实例创建成功
2025-08-04 00:10:07 - INFO - [__init__:431] - 已设置WebSocket URL: wss://fstream.binance.com
2025-08-04 00:10:07 - INFO - [setup:706] - 正在执行启动设置...
2025-08-04 00:10:07 - INFO - [test_connection:528] - 正在测试API连接... (尝试 1/5)
2025-08-04 00:10:07 - INFO - [test_connection:531] - 正在测试基础网络连接...
2025-08-04 00:10:07 - INFO - [test_connection:563] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-08-04 00:10:07 - INFO - [run:2109] - 📊 状态报告器已启动
2025-08-04 00:10:08 - INFO - [test_connection:567] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-08-04 00:10:08 - INFO - [test_connection:578] - 正在测试CCXT交易所连接...
2025-08-04 00:10:08 - INFO - [test_connection:584] - CCXT Ping 成功: {}
2025-08-04 00:10:08 - INFO - [test_connection:598] - 正在测试服务器时间获取...
2025-08-04 00:10:09 - INFO - [test_connection:603] - 服务器时间获取成功: 1754237408985
2025-08-04 00:10:09 - INFO - [test_connection:609] - 正在加载市场数据...
2025-08-04 00:10:13 - INFO - [test_connection:612] - 市场数据加载成功
2025-08-04 00:10:13 - INFO - [test_connection:616] - 交易对 BTC/USDT:USDT 验证成功
2025-08-04 00:10:13 - INFO - [test_connection:651] - 正在测试ticker数据获取...
2025-08-04 00:10:14 - INFO - [test_connection:656] - Ticker 获取成功: BTC/USDT:USDT @ 113779.2
2025-08-04 00:10:14 - INFO - [test_connection:662] - 正在测试账户余额获取...
2025-08-04 00:10:15 - INFO - [test_connection:668] - 余额获取成功，USDT余额: 1201.69283696
2025-08-04 00:10:15 - INFO - [test_connection:678] - API连接测试完成！
2025-08-04 00:10:15 - INFO - [setup:711] - 正在加载市场数据...
2025-08-04 00:10:19 - INFO - [setup:716] - 成功加载市场数据，合约精度: {'amount': 0.001, 'price': 0.1, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-08-04 00:10:19 - INFO - [setup:722] - 已从交易所动态获取最小订单名义价值: 100.0 USDT
2025-08-04 00:10:21 - INFO - [set_hedge_mode:1872] - 当前已是双向持仓模式
2025-08-04 00:10:22 - INFO - [set_leverage:1881] - 杠杆已设置为 20x
2025-08-04 00:10:28 - INFO - [_reconcile_state:1617] - --- 开始状态核对与修复 ---
2025-08-04 00:10:28 - INFO - [_reconcile_state:1657] - --- 状态核对与修复完成 ---
2025-08-04 00:10:29 - INFO - [setup:742] - 全局风控启动：初始权益 = 1201.69 USDT, 峰值权益 = 1201.69 USDT
2025-08-04 00:10:30 - INFO - [_initialize_grid_pairs:1138] - 已成功初始化 39 个固定网格配对。
2025-08-04 00:10:30 - INFO - [_initialize_grid_pairs:1165] - 计划投入保证金: 500.0 USDT, 杠杆: 20x, 总名义价值: 10000.0 USDT
2025-08-04 00:10:30 - INFO - [_initialize_grid_pairs:1166] - 根据 39 个网格配对，已计算出固定订单数量: 0.002169
2025-08-04 00:10:33 - INFO - [setup:765] - 初始化设置完成。
2025-08-04 00:10:33 - INFO - [setup:766] - 当前持仓: 多头=0.0, 空头=0.0
2025-08-04 00:10:33 - INFO - [cancel_all_open_orders:1539] - 正在取消所有 0 个挂单...
2025-08-04 00:10:35 - INFO - [cancel_all_open_orders:1542] - 所有挂单已取消
2025-08-04 00:10:35 - INFO - [run:797] - 所有现有挂单已取消，准备启动策略...
2025-08-04 00:10:35 - INFO - [run:805] - 正在连接WebSocket: wss://fstream.binance.com/ws/j3l3h69WdzHiVzTc5mci1LzyMpoojFonkJk4jzirTEtRJaA6FW4Y2dG5dcYT7SCO
2025-08-04 00:10:35 - ERROR - [run:874] - WebSocket连接失败 (1/10): BaseEventLoop.create_connection() got an unexpected keyword argument 'additional_headers'
2025-08-04 00:10:35 - INFO - [run:884] - 等待 10 秒后重连...
2025-08-04 00:10:46 - INFO - [run:890] - 已获取新的ListenKey
2025-08-04 00:10:46 - INFO - [run:805] - 正在连接WebSocket: wss://fstream.binance.com/ws/j3l3h69WdzHiVzTc5mci1LzyMpoojFonkJk4jzirTEtRJaA6FW4Y2dG5dcYT7SCO
2025-08-04 00:10:46 - ERROR - [run:874] - WebSocket连接失败 (2/10): BaseEventLoop.create_connection() got an unexpected keyword argument 'additional_headers'
2025-08-04 00:10:46 - INFO - [run:884] - 等待 20 秒后重连...
2025-08-04 00:11:07 - INFO - [run:890] - 已获取新的ListenKey
2025-08-04 00:11:07 - INFO - [run:805] - 正在连接WebSocket: wss://fstream.binance.com/ws/j3l3h69WdzHiVzTc5mci1LzyMpoojFonkJk4jzirTEtRJaA6FW4Y2dG5dcYT7SCO
2025-08-04 00:11:07 - ERROR - [run:874] - WebSocket连接失败 (3/10): BaseEventLoop.create_connection() got an unexpected keyword argument 'additional_headers'
2025-08-04 00:11:07 - INFO - [run:884] - 等待 40 秒后重连...
2025-08-04 00:11:25 - INFO - [close:900] - 正在关闭程序...
2025-08-04 00:11:25 - INFO - [cancel_all_open_orders:1539] - 正在取消所有 0 个挂单...
2025-08-04 00:11:27 - INFO - [cancel_all_open_orders:1542] - 所有挂单已取消
2025-08-04 00:11:27 - INFO - [close:929] - 程序已关闭。
2025-08-04 00:19:55 - INFO - [main:2286] - 启动 ARGM-V6.1 策略...
2025-08-04 00:19:55 - INFO - [_initialize_exchange:514] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-08-04 00:19:55 - INFO - [_initialize_exchange:517] - 交易所实例创建成功
2025-08-04 00:19:55 - INFO - [__init__:431] - 已设置WebSocket URL: wss://fstream.binance.com
2025-08-04 00:19:55 - INFO - [setup:705] - 正在执行启动设置...
2025-08-04 00:19:55 - INFO - [test_connection:527] - 正在测试API连接... (尝试 1/5)
2025-08-04 00:19:55 - INFO - [test_connection:530] - 正在测试基础网络连接...
2025-08-04 00:19:55 - INFO - [test_connection:562] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-08-04 00:19:55 - INFO - [run:2140] - 📊 状态报告器已启动
2025-08-04 00:19:56 - INFO - [test_connection:566] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-08-04 00:19:56 - INFO - [test_connection:577] - 正在测试CCXT交易所连接...
2025-08-04 00:19:56 - INFO - [test_connection:583] - CCXT Ping 成功: {}
2025-08-04 00:19:56 - INFO - [test_connection:597] - 正在测试服务器时间获取...
2025-08-04 00:19:57 - INFO - [test_connection:602] - 服务器时间获取成功: 1754237997154
2025-08-04 00:19:57 - INFO - [test_connection:608] - 正在加载市场数据...
2025-08-04 00:20:01 - INFO - [test_connection:611] - 市场数据加载成功
2025-08-04 00:20:01 - INFO - [test_connection:615] - 交易对 BTC/USDT:USDT 验证成功
2025-08-04 00:20:01 - INFO - [test_connection:650] - 正在测试ticker数据获取...
2025-08-04 00:20:02 - INFO - [test_connection:655] - Ticker 获取成功: BTC/USDT:USDT @ 113793.1
2025-08-04 00:20:02 - INFO - [test_connection:661] - 正在测试账户余额获取...
2025-08-04 00:20:03 - INFO - [test_connection:667] - 余额获取成功，USDT余额: 1201.69283696
2025-08-04 00:20:03 - INFO - [test_connection:677] - API连接测试完成！
2025-08-04 00:20:03 - INFO - [setup:710] - 正在加载市场数据...
2025-08-04 00:20:07 - INFO - [setup:715] - 成功加载市场数据，合约精度: {'amount': 0.001, 'price': 0.1, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-08-04 00:20:07 - INFO - [setup:721] - 已从交易所动态获取最小订单名义价值: 100.0 USDT
2025-08-04 00:20:09 - INFO - [set_hedge_mode:1903] - 当前已是双向持仓模式
2025-08-04 00:20:10 - INFO - [set_leverage:1912] - 杠杆已设置为 20x
2025-08-04 00:20:16 - INFO - [_reconcile_state:1658] - --- 开始状态核对与修复 ---
2025-08-04 00:20:16 - INFO - [_reconcile_state:1698] - --- 状态核对与修复完成 ---
2025-08-04 00:20:17 - INFO - [setup:741] - 全局风控启动：初始权益 = 1201.69 USDT, 峰值权益 = 1201.69 USDT
2025-08-04 00:20:19 - INFO - [_initialize_grid_pairs:1133] - ✅ 已成功初始化 39 个固定网格配对
2025-08-04 00:20:19 - INFO - [_initialize_grid_pairs:1152] - 💰 资金配置: 投资 500.0 USDT × 20x 杠杆 = 10000.0 USDT 总名义价值
2025-08-04 00:20:19 - INFO - [_initialize_grid_pairs:1153] - 📊 网格范围: 115800.00 - 120800.00, 共 39 个配对
2025-08-04 00:20:19 - INFO - [_initialize_grid_pairs:1154] - 🔄 动态订单管理: 将根据实时价格和资金使用情况动态分配订单数量
2025-08-04 00:20:21 - INFO - [setup:764] - 初始化设置完成。
2025-08-04 00:20:21 - INFO - [setup:765] - 当前持仓: 多头=0.0, 空头=0.0
2025-08-04 00:20:21 - INFO - [cancel_all_open_orders:1580] - 正在取消所有 0 个挂单...
2025-08-04 00:20:23 - INFO - [cancel_all_open_orders:1583] - 所有挂单已取消
2025-08-04 00:20:23 - INFO - [run:796] - 所有现有挂单已取消，准备启动策略...
2025-08-04 00:20:23 - INFO - [run:804] - 正在连接WebSocket: wss://fstream.binance.com/ws/j3l3h69WdzHiVzTc5mci1LzyMpoojFonkJk4jzirTEtRJaA6FW4Y2dG5dcYT7SCO
