import pandas as pd
import numpy as np
import time

# --- 策略与回测配置 ---
CONFIG = {
    "backtest": {
        "initial_capital": 500.0,  # 初始投入的保证金 (USDT)
        "data_path": "path/to/your/btc_minute_data.csv" # ❗❗❗ 替换为您的数据文件路径
    },
    "strategy": {
        "leverage": 20,           # 杠杆倍数
        "grid_count": 40,         # 网格数量
        
        # --- [核心] 自适应区间参数 ---
        "adaptive_range": {
            "enabled": True,
            "ema_period": 24 * 60,      # 网格中枢的EMA周期 (24小时)
            "atr_period": 24 * 60,      # 计算波动率的ATR周期 (24小时)
            "atr_multiplier": 2.0,      # ATR倍数，决定网格宽度
            "repaint_threshold": 0.05   # 价格偏离中枢5%后，重绘网格
        },
    }
}


def load_data(filepath):
    """加载并预处理分钟级数据"""
    print(f"Loading data from {filepath}...")
    try:
        df = pd.read_csv(filepath, parse_dates=['date'], index_col='date')
    except Exception as e:
        print(f"Error loading data: {e}")
        print("Please ensure the CSV file path is correct and it contains a 'date' column.")
        return None
        
    df.sort_index(inplace=True)
    df.rename(columns={
        'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close', 'Volume USDT': 'Volume'
    }, inplace=True)
    print(f"Data loaded successfully. Shape: {df.shape}")
    return df[['Open', 'High', 'Low', 'Close', 'Volume']]


class GridBacktester:
    """
    一个用于网格策略的、基于事件驱动的简化回测器。
    """
    def __init__(self, data, config):
        self.data = data
        self.config = config
        self.strategy_cfg = config['strategy']
        self.backtest_cfg = config['backtest']
        self._calculate_indicators()
        
        # 状态变量
        self.balance = self.backtest_cfg['initial_capital']
        self.initial_balance = self.backtest_cfg['initial_capital']
        self.position_size = 0.0  # 合约数量
        self.avg_entry_price = 0.0
        self.liquidation_price = 0.0
        self.total_profit = 0.0
        self.trades = []
        self.equity_curve = []

        # 网格状态
        self.grid_buy_orders = {}  # price -> quantity
        self.grid_sell_orders = {} # price -> quantity
        self.grid_center = 0

    def _calculate_indicators(self):
        """预计算技术指标以提高速度"""
        print("Calculating indicators (EMA, ATR)...")
        adaptive_cfg = self.strategy_cfg['adaptive_range']
        self.data['ema'] = self.data['Close'].ewm(span=adaptive_cfg['ema_period'], adjust=False).mean()
        
        high_low = self.data['High'] - self.data['Low']
        high_close = np.abs(self.data['High'] - self.data['Close'].shift())
        low_close = np.abs(self.data['Low'] - self.data['Close'].shift())
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        self.data['atr'] = tr.ewm(span=adaptive_cfg['atr_period'], adjust=False).mean()
        print("Indicators calculated.")

    def _update_grid_range(self, current_price, ema, atr):
        """根据当前市场情况，动态生成或更新网格区间"""
        adaptive_cfg = self.strategy_cfg['adaptive_range']
        
        self.grid_center = ema
        grid_range_half = atr * adaptive_cfg['atr_multiplier']
        range_high = self.grid_center + grid_range_half
        range_low = self.grid_center - grid_range_half
        
        grid_levels = np.linspace(range_low, range_high, self.strategy_cfg['grid_count'])
        
        # 清空旧的订单
        self.grid_buy_orders.clear()
        self.grid_sell_orders.clear()
        
        # 名义价值 = 初始资本 * 杠杆
        total_notional_value = self.initial_balance * self.strategy_cfg['leverage']
        notional_per_grid = total_notional_value / self.strategy_cfg['grid_count']

        for price in grid_levels:
            if price < current_price:
                # 在当前价格下方设置买单
                self.grid_buy_orders[price] = notional_per_grid / price
            else:
                # 在当前价格上方设置卖单 (这里是为未来持仓准备的，初始为空)
                pass # 初始不挂卖单

    def _calculate_liquidation_price(self):
        """计算当前仓位的理论爆仓价"""
        if self.position_size == 0:
            self.liquidation_price = 0
            return
        
        leverage = self.strategy_cfg['leverage']
        # 简化公式: 爆仓价 ≈ 开仓均价 * (1 - 1/杠杆 + 维持保证金率)
        # 我们这里忽略维持保证金率，用一个更保守的近似值
        self.liquidation_price = self.avg_entry_price * (1 - 1 / leverage)

    def run(self):
        """主回测循环"""
        start_time = time.time()
        print("Starting backtest run...")
        
        for i, row in self.data.iterrows():
            current_price = row['Close']
            low_price = row['Low']
            high_price = row['High']

            # 1. 检查爆仓 (最优先)
            if self.position_size > 0 and low_price <= self.liquidation_price:
                print(f"\n--- LIQUIDATION EVENT on {i.strftime('%Y-%m-%d %H:%M')} ---")
                print(f"Price dropped to {low_price:.2f}, triggering liquidation at {self.liquidation_price:.2f}")
                print(f"Final Balance: 0, Loss: {self.balance:.2f}")
                self.balance = 0
                self.equity_curve.append(self.balance)
                break # 本次生命周期结束

            # 2. 决定是否重绘网格
            # 如果没有仓位，或价格偏离中枢太远，则重绘
            should_repaint = (self.position_size == 0 and not self.grid_buy_orders) or \
                             (abs(current_price - self.grid_center) / self.grid_center > self.strategy_cfg['adaptive_range']['repaint_threshold'])

            if should_repaint and self.position_size == 0: # 只有空仓时才允许重绘
                self._update_grid_range(current_price, row['ema'], row['atr'])

            # 3. 检查卖单成交 (先检查卖，减少持仓风险)
            for sell_price in sorted(list(self.grid_sell_orders.keys())):
                if high_price >= sell_price:
                    sold_qty = self.grid_sell_orders.pop(sell_price)
                    
                    # 确保我们不会卖得比持仓多
                    sold_qty = min(sold_qty, self.position_size)
                    if sold_qty <= 0: continue

                    # 找到与之配对的买单价格来计算利润
                    buy_price = sell_price - (self.grid_sell_orders.get(sell_price-0.01, sell_price-0.01)) # 简化假设
                    # 精确计算需要找到对应的买入成本，这里做简化
                    profit = (sell_price - self.avg_entry_price) * sold_qty 
                    self.total_profit += profit
                    self.balance += profit

                    # 更新仓位
                    self.position_size -= sold_qty
                    # 仓位成本的更新很复杂，这里简化为0如果仓位空了
                    if self.position_size < 1e-8: self.avg_entry_price = 0
                    
                    self.trades.append({'date': i, 'type': 'sell', 'price': sell_price, 'qty': sold_qty, 'profit': profit})
                    
            # 4. 检查买单成交
            for buy_price in sorted(list(self.grid_buy_orders.keys()), reverse=True):
                if low_price <= buy_price:
                    buy_qty = self.grid_buy_orders.pop(buy_price)
                    
                    # 更新仓位
                    new_cost = self.avg_entry_price * self.position_size + buy_price * buy_qty
                    self.position_size += buy_qty
                    self.avg_entry_price = new_cost / self.position_size

                    # 为这个买单挂上对应的卖单
                    sell_price = buy_price + (self.data.loc[i, 'atr'] * self.strategy_cfg['adaptive_range']['atr_multiplier'] / self.strategy_cfg['grid_count'])
                    self.grid_sell_orders[sell_price] = buy_qty

                    self.trades.append({'date': i, 'type': 'buy', 'price': buy_price, 'qty': buy_qty, 'profit': 0})
            
            # 5. 更新状态
            self._calculate_liquidation_price()
            current_equity = self.balance + (current_price - self.avg_entry_price) * self.position_size if self.position_size > 0 else self.balance
            self.equity_curve.append(current_equity)

            if i.minute == 0 and i.hour == 0: # 每天打印一次进度
                print(f"\rProcessing: {i.strftime('%Y-%m-%d')} | Equity: {current_equity:.2f} | Pos Size: {self.position_size:.4f}", end="")

        print(f"\nBacktest finished in {time.time() - start_time:.2f} seconds.")
        self._generate_report()

    def _generate_report(self):
        """生成并打印回测绩效报告"""
        print("\n--- Backtest Performance Report ---")
        
        equity_series = pd.Series(self.equity_curve)
        
        total_return = (self.balance / self.initial_balance - 1) * 100
        
        # 计算最大回撤
        peak = equity_series.expanding(min_periods=1).max()
        drawdown = (equity_series - peak) / peak
        max_drawdown = drawdown.min() * 100
        
        print(f"Initial Capital:      {self.initial_balance:,.2f} USDT")
        print(f"Final Balance:        {self.balance:,.2f} USDT")
        print(f"Total Net Profit:     {self.balance - self.initial_balance:,.2f} USDT")
        print(f"Total Return:         {total_return:.2f}%")
        print("-" * 35)
        print(f"Total Trades:         {len(self.trades)}")
        print(f"Total Grid Profit:    {self.total_profit:,.2f} USDT")
        print(f"Max Drawdown:         {max_drawdown:.2f}%")
        
        if self.balance == 0:
            print("\nResult: STRATEGY WAS LIQUIDATED.")
        else:
             print("\nResult: STRATEGY SURVIVED THE BACKTEST PERIOD.")


if __name__ == "__main__":
    # 确保配置文件中的路径是正确的
    data_df = load_data(CONFIG['backtest']['data_path'])
    
    if data_df is not None:
        # 为了快速演示，可以只截取一部分数据进行回测
        # data_df = data_df.loc['2021-01-01':'2021-03-31']
        
        backtester = GridBacktester(data_df, CONFIG)
        backtester.run()