# 市场扫描器优化实现计划

## 实现计划

- [ ] 1. 创建核心数据结构和配置管理
  - 实现简化的配置类OptimizedConfig，包含最小化的核心参数
  - 创建MarketSignal和StrategyRecommendation数据类
  - 实现SimplifiedMarketData类作为核心数据容器
  - _需求: 1.1, 8.1, 8.2_

- [ ] 2. 实现优化的数据获取层
  - 创建OptimizedDataFetcher类，支持批量数据获取和智能缓存
  - 实现SymbolFilter类，筛选活跃的USDT永续合约
  - 添加数据缓存机制，减少API调用频率
  - 实现异常处理和重试机制
  - _需求: 6.1, 6.4, 1.5_

- [ ] 3. 重构核心指标计算引擎
  - 创建CoreIndicators类，只计算四个核心指标（动量、RSI、成交量比率、相对强度）
  - 移除复杂指标（资金费率、持仓变化、ADX、布林带等）
  - 实现滚动窗口Z-Score标准化替代全市场静态标准化
  - 优化指标计算性能，使用向量化操作
  - _需求: 1.1, 1.2, 1.3, 6.2_

- [ ] 4. 实现策略分类器核心逻辑
  - 创建StrategyClassifier基类和三个策略子类
  - 实现TrendStrategyClassifier，识别DCA抄底、趋势跟踪、区间突破机会
  - 实现RangeStrategyClassifier，识别适合网格交易的品种
  - 实现PairStrategyClassifier，生成强势主流币和弱势山寨币配对
  - _需求: 2.1, 2.2, 2.4, 2.5_

- [ ] 5. 开发趋势策略信号生成器
  - 实现DCABottomFinder，识别超跌但基本面强劲的主流币
  - 实现TrendFollower，识别高动量且突破确认的品种
  - 实现BreakoutDetector，识别接近阻力位且成交量放大的品种
  - 为每个信号添加强度评分和置信度计算
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [ ] 6. 开发震荡策略信号生成器
  - 实现GridSuitabilityAnalyzer，评估品种的网格交易适用性
  - 实现RangeIdentifier，识别明确的支撑阻力区间
  - 实现VolatilityAnalyzer，分析波动率稳定性
  - 添加流动性评估和风险评估功能
  - _需求: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 7. 开发配对策略信号生成器
  - 实现MaincoinSelector，从BTC、ETH、BNB等主流币中选择强势品种
  - 实现AltcoinSelector，从山寨币中选择弱势品种
  - 实现PairMatcher，生成风险平衡的多空配对组合
  - 添加相关性分析和仓位比例建议功能
  - _需求: 5.1, 5.2, 5.3, 5.4, 5.5_

- [ ] 8. 实现统一的信号处理引擎
  - 创建SignalProcessor类，整合三种策略的信号生成
  - 实现信号强度评分和排序逻辑
  - 添加信号去重和冲突处理机制
  - 实现每策略最多5个推荐标的的限制
  - _需求: 2.3, 2.4, 2.5_

- [ ] 9. 创建简化的输出界面
  - 实现SimplifiedDisplay类，替代复杂的Rich表格显示
  - 按三种策略分别展示推荐标的，每种策略最多显示3-5个
  - 只显示关键指标：符号、信号强度、入场价格、风险等级
  - 添加清晰的时间戳和更新状态显示
  - _需求: 7.1, 7.2, 7.3, 7.4_

- [ ] 10. 实现性能优化和错误处理
  - 创建ErrorHandler类，实现分层错误处理策略
  - 优化内存使用，及时清理过期数据
  - 实现API限流控制和指数退避重试
  - 添加性能监控和资源使用统计
  - _需求: 6.1, 6.2, 6.3, 1.5_

- [ ] 11. 实现配置管理和参数验证
  - 创建ConfigValidator类，验证参数合理性
  - 实现配置文件的加载和保存功能
  - 添加策略切换和参数热更新支持
  - 实现配置历史记录和回滚功能
  - _需求: 8.1, 8.3, 8.4, 8.5_

- [ ] 12. 创建主程序和监控循环
  - 重构main函数，使用新的架构和组件
  - 实现优化的扫描循环，支持动态频率调整
  - 添加程序启动时的参数验证和系统检查
  - 实现优雅的程序退出和资源清理
  - _需求: 6.4, 8.4_

- [ ] 13. 添加历史记录和数据持久化
  - 实现信号历史记录保存功能
  - 创建简单的CSV格式数据导出
  - 添加关键事件日志记录
  - 实现数据清理和归档机制
  - _需求: 7.5_

- [ ] 14. 编写单元测试和集成测试
  - 为核心指标计算编写单元测试
  - 为策略分类逻辑编写测试用例
  - 创建端到端的信号生成测试
  - 添加性能测试和压力测试
  - _需求: 所有需求的验证_

- [ ] 15. 创建使用文档和配置示例
  - 编写简化的使用说明文档
  - 创建不同策略的配置示例
  - 添加常见问题解答和故障排除指南
  - 提供与现有交易策略的集成建议
  - _需求: 8.1, 8.2_