2025-07-24 23:05:54,970 - INFO - [main] - 启动 ARGM-V6.1 策略...
2025-07-24 23:05:54,971 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-24 23:05:54,978 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-24 23:05:54,978 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-24 23:05:54,979 - INFO - [setup] - 正在执行启动设置...
2025-07-24 23:05:54,979 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-24 23:05:54,979 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-24 23:05:54,987 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-24 23:05:55,067 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-24 23:05:55,067 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-24 23:05:55,541 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-24 23:05:55,542 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-24 23:05:55,872 - INFO - [test_connection] - 服务器时间获取成功: 1753369555827
2025-07-24 23:05:55,872 - INFO - [test_connection] - 正在加载市场数据...
2025-07-24 23:05:59,891 - INFO - [test_connection] - 市场数据加载成功
2025-07-24 23:05:59,891 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-24 23:05:59,892 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-24 23:06:01,164 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 190.16
2025-07-24 23:06:01,164 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-24 23:06:02,424 - INFO - [test_connection] - 余额获取成功，USDT余额: 1657.64037018
2025-07-24 23:06:02,424 - INFO - [test_connection] - API连接测试完成！
2025-07-24 23:06:02,424 - INFO - [setup] - 正在加载市场数据...
2025-07-24 23:06:06,556 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-24 23:06:07,807 - INFO - [set_hedge_mode] - 当前已是双向持仓模式
2025-07-24 23:06:09,085 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-24 23:06:16,404 - INFO - [setup] - 全局风控启动：初始权益 = 1657.64 USDT, 峰值权益 = 1657.64 USDT
2025-07-24 23:06:20,122 - INFO - [setup] - 初始化设置完成。
2025-07-24 23:06:20,123 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-24 23:06:20,123 - INFO - [cancel_all_open_orders] - 正在取消所有 0 个挂单...
2025-07-24 23:06:21,388 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-24 23:06:21,388 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-24 23:06:21,389 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/PVMFls4fOf3OYvMFolRkByo3X13E79ls9Jt90ayqlbNYHf7dGv1Mg8mBYBWz6PDk
2025-07-24 23:06:21,595 - INFO - [run] - WebSocket 连接成功。
2025-07-24 23:06:21,596 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-24 23:06:25,275 - INFO - [generate_grid_state] - 固定网格已生成: 20个点位，范围 [180.0, 200.0]
2025-07-24 23:06:25,275 - INFO - [execute_strategy] - 网格重绘触发！
2025-07-24 23:06:25,276 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-24 23:06:25,276 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-24 23:06:30,156 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-24 23:06:30,157 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建20个
2025-07-24 23:06:30,157 - INFO - [place_order] - 尝试下单: buy LONG 0.1000 @ 180.0000
2025-07-24 23:06:32,610 - INFO - [place_order] - 下单成功: x-argm-1753369590157-3106
2025-07-24 23:06:32,717 - INFO - [place_order] - 尝试下单: buy LONG 0.1000 @ 181.0500
2025-07-24 23:06:37,584 - INFO - [place_order] - 下单成功: x-argm-1753369592717-8595
2025-07-24 23:06:37,686 - INFO - [place_order] - 尝试下单: buy LONG 0.1000 @ 182.1100
2025-07-24 23:06:42,552 - INFO - [place_order] - 下单成功: x-argm-1753369597686-2988
2025-07-24 23:06:42,657 - INFO - [place_order] - 尝试下单: buy LONG 0.1000 @ 183.1600
2025-07-24 23:06:43,183 - INFO - [close] - 正在关闭程序...
2025-07-24 23:06:43,183 - INFO - [cancel_all_open_orders] - 正在取消所有 3 个挂单...
2025-07-24 23:16:53,195 - INFO - [main] - 启动 ARGM-V6.1 策略...
2025-07-24 23:16:53,195 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-24 23:16:53,202 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-24 23:16:53,202 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-24 23:16:53,204 - INFO - [setup] - 正在执行启动设置...
2025-07-24 23:16:53,204 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-24 23:16:53,205 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-24 23:16:53,212 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-24 23:16:53,297 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-24 23:16:53,297 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-24 23:16:53,776 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-24 23:16:53,776 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-24 23:16:54,038 - INFO - [close] - 正在关闭程序...
2025-07-24 23:16:54,293 - INFO - [close] - 程序已关闭。
2025-07-24 23:17:13,649 - INFO - [main] - 启动 ARGM-V6.1 策略...
2025-07-24 23:17:13,650 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-24 23:17:13,658 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-24 23:17:13,658 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-24 23:17:13,660 - INFO - [setup] - 正在执行启动设置...
2025-07-24 23:17:13,660 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-24 23:17:13,660 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-24 23:17:13,671 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-24 23:17:13,756 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-24 23:17:13,757 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-24 23:17:14,225 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-24 23:17:14,226 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-24 23:17:14,568 - INFO - [test_connection] - 服务器时间获取成功: 1753370234522
2025-07-24 23:17:14,568 - INFO - [test_connection] - 正在加载市场数据...
2025-07-24 23:17:18,596 - INFO - [test_connection] - 市场数据加载成功
2025-07-24 23:17:18,596 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-24 23:17:18,597 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-24 23:17:19,863 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 190.25
2025-07-24 23:17:19,863 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-24 23:17:21,130 - INFO - [test_connection] - 余额获取成功，USDT余额: 1657.64037018
2025-07-24 23:17:21,130 - INFO - [test_connection] - API连接测试完成！
2025-07-24 23:17:21,130 - INFO - [setup] - 正在加载市场数据...
2025-07-24 23:17:25,244 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-24 23:17:26,501 - INFO - [set_hedge_mode] - 当前已是双向持仓模式
2025-07-24 23:17:27,768 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-24 23:17:33,830 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-24 23:17:33,831 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-24 23:17:35,097 - INFO - [setup] - 全局风控启动：初始权益 = 1657.64 USDT, 峰值权益 = 1657.64 USDT
2025-07-24 23:17:36,367 - INFO - [_initialize_grid_pairs] - 已成功初始化 19 个固定网格配对。
2025-07-24 23:17:38,843 - INFO - [setup] - 初始化设置完成。
2025-07-24 23:17:38,844 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-24 23:17:38,844 - INFO - [cancel_all_open_orders] - 正在取消所有 0 个挂单...
2025-07-24 23:17:40,102 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-24 23:17:40,102 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-24 23:17:40,102 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/PVMFls4fOf3OYvMFolRkByo3X13E79ls9Jt90ayqlbNYHf7dGv1Mg8mBYBWz6PDk
2025-07-24 23:17:40,244 - INFO - [run] - WebSocket 连接成功。
2025-07-24 23:17:40,244 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-24 23:17:43,929 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-24 23:17:43,930 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-24 23:17:43,931 - INFO - [generate_grid_state] - 固定网格已生成: 20个点位，范围 [180.0, 200.0]
2025-07-24 23:17:43,931 - INFO - [execute_strategy] - 网格重绘触发！
2025-07-24 23:17:43,931 - INFO - [place_new_buy_orders] - 准备为 10 个空的网格配对创建买单，统一数量: 0.027066
2025-07-24 23:17:43,931 - INFO - [place_order] - 尝试下单: buy LONG 0.0200 @ 180.0000
2025-07-24 23:17:46,385 - ERROR - [place_order] - 下单失败: binanceusdm {"code":-4164,"msg":"Order's notional must be no smaller than 5 (unless you choose reduce only)."}
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3.py", line 1084, in place_order
    new_order = await asyncio.wait_for(
                ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 6024, in create_order
    response = await self.fapiPrivatePostOrder(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 934, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 252, in fetch
    self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11306, in handle_errors
    self.throw_exactly_matched_exception(self.get_exceptions_by_url(url, 'exact'), error, feedback)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\exchange.py", line 4920, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.InvalidOrder: binanceusdm {"code":-4164,"msg":"Order's notional must be no smaller than 5 (unless you choose reduce only)."}
2025-07-24 23:17:46,390 - ERROR - [place_new_buy_orders] - 为配对 180.0 创建买单失败: binanceusdm {"code":-4164,"msg":"Order's notional must be no smaller than 5 (unless you choose reduce only)."}
2025-07-24 23:17:46,390 - INFO - [place_order] - 尝试下单: buy LONG 0.0200 @ 181.0500
2025-07-24 23:17:51,254 - ERROR - [place_order] - 下单失败: binanceusdm {"code":-4164,"msg":"Order's notional must be no smaller than 5 (unless you choose reduce only)."}
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3.py", line 1084, in place_order
    new_order = await asyncio.wait_for(
                ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 6024, in create_order
    response = await self.fapiPrivatePostOrder(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 934, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 252, in fetch
    self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11306, in handle_errors
    self.throw_exactly_matched_exception(self.get_exceptions_by_url(url, 'exact'), error, feedback)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\exchange.py", line 4920, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.InvalidOrder: binanceusdm {"code":-4164,"msg":"Order's notional must be no smaller than 5 (unless you choose reduce only)."}
2025-07-24 23:17:51,256 - ERROR - [place_new_buy_orders] - 为配对 181.05 创建买单失败: binanceusdm {"code":-4164,"msg":"Order's notional must be no smaller than 5 (unless you choose reduce only)."}
2025-07-24 23:17:51,256 - INFO - [place_order] - 尝试下单: buy LONG 0.0200 @ 182.1100
2025-07-24 23:17:56,128 - ERROR - [place_order] - 下单失败: binanceusdm {"code":-4164,"msg":"Order's notional must be no smaller than 5 (unless you choose reduce only)."}
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3.py", line 1084, in place_order
    new_order = await asyncio.wait_for(
                ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 6024, in create_order
    response = await self.fapiPrivatePostOrder(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 934, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 252, in fetch
    self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11306, in handle_errors
    self.throw_exactly_matched_exception(self.get_exceptions_by_url(url, 'exact'), error, feedback)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\exchange.py", line 4920, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.InvalidOrder: binanceusdm {"code":-4164,"msg":"Order's notional must be no smaller than 5 (unless you choose reduce only)."}
2025-07-24 23:17:56,130 - ERROR - [place_new_buy_orders] - 为配对 182.11 创建买单失败: binanceusdm {"code":-4164,"msg":"Order's notional must be no smaller than 5 (unless you choose reduce only)."}
2025-07-24 23:17:56,130 - INFO - [place_order] - 尝试下单: buy LONG 0.0200 @ 183.1600
2025-07-24 23:17:59,430 - INFO - [close] - 正在关闭程序...
2025-07-24 23:17:59,431 - INFO - [cancel_all_open_orders] - 正在取消所有 0 个挂单...
2025-07-24 23:18:09,429 - WARNING - [close] - 关闭时取消挂单失败，但程序将继续关闭: 
2025-07-24 23:18:09,693 - INFO - [close] - 程序已关闭。
2025-07-24 23:29:34,152 - INFO - [main] - 启动 ARGM-V6.1 策略...
2025-07-24 23:29:34,152 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-24 23:29:34,160 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-24 23:29:34,160 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-24 23:29:34,161 - INFO - [setup] - 正在执行启动设置...
2025-07-24 23:29:34,161 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-24 23:29:34,161 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-24 23:29:34,168 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-24 23:29:34,263 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-24 23:29:34,263 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-24 23:29:34,786 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-24 23:29:34,786 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-24 23:29:35,120 - INFO - [test_connection] - 服务器时间获取成功: 1753370975073
2025-07-24 23:29:35,121 - INFO - [test_connection] - 正在加载市场数据...
2025-07-24 23:29:39,160 - INFO - [test_connection] - 市场数据加载成功
2025-07-24 23:29:39,160 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-24 23:29:39,160 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-24 23:29:40,421 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 189.15
2025-07-24 23:29:40,421 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-24 23:29:41,676 - INFO - [test_connection] - 余额获取成功，USDT余额: 1657.64037018
2025-07-24 23:29:41,677 - INFO - [test_connection] - API连接测试完成！
2025-07-24 23:29:41,677 - INFO - [setup] - 正在加载市场数据...
2025-07-24 23:29:45,794 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-24 23:29:47,069 - INFO - [set_hedge_mode] - 当前已是双向持仓模式
2025-07-24 23:29:48,337 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-24 23:29:54,405 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-24 23:29:54,406 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-24 23:29:55,664 - INFO - [setup] - 全局风控启动：初始权益 = 1657.64 USDT, 峰值权益 = 1657.64 USDT
2025-07-24 23:29:56,938 - INFO - [_initialize_grid_pairs] - 已成功初始化 19 个固定网格配对。
2025-07-24 23:29:59,396 - INFO - [setup] - 初始化设置完成。
2025-07-24 23:29:59,396 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-24 23:29:59,397 - INFO - [cancel_all_open_orders] - 正在取消所有 0 个挂单...
2025-07-24 23:30:00,661 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-24 23:30:00,661 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-24 23:30:00,661 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/PVMFls4fOf3OYvMFolRkByo3X13E79ls9Jt90ayqlbNYHf7dGv1Mg8mBYBWz6PDk
2025-07-24 23:30:01,097 - INFO - [run] - WebSocket 连接成功。
2025-07-24 23:30:01,097 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-24 23:30:04,772 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-24 23:30:04,773 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-24 23:30:04,773 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:30:04,773 - INFO - [place_new_buy_orders] - 准备为 9 个空的网格配对创建买单，统一数量: 0.241269
2025-07-24 23:30:04,774 - INFO - [place_order] - 尝试下单: buy LONG 0.2400 @ 180.0000
2025-07-24 23:30:07,278 - INFO - [place_order] - 下单成功: x-argm-1753371004774-05a1
2025-07-24 23:30:07,278 - INFO - [place_new_buy_orders] - 为配对 180.0->181.05 创建买单成功。
2025-07-24 23:30:07,380 - INFO - [place_order] - 尝试下单: buy LONG 0.2400 @ 181.0500
2025-07-24 23:30:12,251 - INFO - [place_order] - 下单成功: x-argm-1753371007380-270d
2025-07-24 23:30:12,251 - INFO - [place_new_buy_orders] - 为配对 181.05->182.11 创建买单成功。
2025-07-24 23:30:12,356 - INFO - [place_order] - 尝试下单: buy LONG 0.2400 @ 182.1100
2025-07-24 23:30:17,221 - INFO - [place_order] - 下单成功: x-argm-1753371012356-009f
2025-07-24 23:30:17,222 - INFO - [place_new_buy_orders] - 为配对 182.11->183.16 创建买单成功。
2025-07-24 23:30:17,326 - INFO - [place_order] - 尝试下单: buy LONG 0.2400 @ 183.1600
2025-07-24 23:30:22,199 - INFO - [place_order] - 下单成功: x-argm-1753371017326-f5eb
2025-07-24 23:30:22,199 - INFO - [place_new_buy_orders] - 为配对 183.16->184.21 创建买单成功。
2025-07-24 23:30:22,305 - INFO - [place_order] - 尝试下单: buy LONG 0.2400 @ 184.2100
2025-07-24 23:30:27,167 - INFO - [place_order] - 下单成功: x-argm-1753371022305-b671
2025-07-24 23:30:27,167 - INFO - [place_new_buy_orders] - 为配对 184.21->185.26 创建买单成功。
2025-07-24 23:30:27,272 - INFO - [place_order] - 尝试下单: buy LONG 0.2400 @ 185.2600
2025-07-24 23:30:32,185 - INFO - [place_order] - 下单成功: x-argm-1753371027272-2289
2025-07-24 23:30:32,185 - INFO - [place_new_buy_orders] - 为配对 185.26->186.32 创建买单成功。
2025-07-24 23:30:32,286 - INFO - [place_order] - 尝试下单: buy LONG 0.2400 @ 186.3200
2025-07-24 23:30:37,154 - INFO - [place_order] - 下单成功: x-argm-1753371032286-8cb5
2025-07-24 23:30:37,154 - INFO - [place_new_buy_orders] - 为配对 186.32->187.37 创建买单成功。
2025-07-24 23:30:37,259 - INFO - [place_order] - 尝试下单: buy LONG 0.2400 @ 187.3700
2025-07-24 23:30:42,117 - INFO - [place_order] - 下单成功: x-argm-1753371037259-85d3
2025-07-24 23:30:42,117 - INFO - [place_new_buy_orders] - 为配对 187.37->188.42 创建买单成功。
2025-07-24 23:30:42,222 - INFO - [place_order] - 尝试下单: buy LONG 0.2400 @ 188.4200
2025-07-24 23:30:47,094 - INFO - [place_order] - 下单成功: x-argm-*************-6e57
2025-07-24 23:30:47,095 - INFO - [place_new_buy_orders] - 为配对 188.42->189.47 创建买单成功。
2025-07-24 23:30:47,196 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:30:51,116 - WARNING - [run] - WebSocket连接已关闭 (1006): 
2025-07-24 23:30:51,116 - INFO - [run] - 等待 5 秒后重连...
2025-07-24 23:31:00,991 - INFO - [run] - 已获取新的ListenKey
2025-07-24 23:31:00,992 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/PVMFls4fOf3OYvMFolRkByo3X13E79ls9Jt90ayqlbNYHf7dGv1Mg8mBYBWz6PDk
2025-07-24 23:31:01,122 - INFO - [run] - WebSocket 连接成功。
2025-07-24 23:31:01,122 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-24 23:31:02,188 - INFO - [monitor_account_drawdown] - 权益更新: 当前=1657.63, 峰值=1657.64, 回撤=0.00%
2025-07-24 23:31:05,801 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-24 23:31:05,801 - WARNING - [_reconcile_state] - 核对异常: 配对 188.42 状态为 BUY_PLACED，但交易所无此挂单 (x-argm-*************-6e57)。重置为空闲。
2025-07-24 23:31:05,801 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-24 23:31:05,802 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:05,802 - INFO - [place_new_buy_orders] - 准备为 1 个空的网格配对创建买单，统一数量: 2.122917
2025-07-24 23:31:05,802 - INFO - [place_order] - 尝试下单: buy LONG 2.1200 @ 188.4200
2025-07-24 23:31:08,267 - INFO - [place_order] - 下单成功: x-argm-*************-cf43
2025-07-24 23:31:08,267 - INFO - [place_new_buy_orders] - 为配对 188.42->189.47 创建买单成功。
2025-07-24 23:31:08,378 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:10,796 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:11,835 - INFO - [handle_order_update] - 配对订单更新: Pair 188.42->189.47, Side: BUY, Status: NEW
2025-07-24 23:31:13,351 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:15,894 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:18,490 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:20,766 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:22,874 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:25,194 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:25,386 - INFO - [handle_websocket_message] - 收到账户更新: {'e': 'ACCOUNT_UPDATE', 'T': *************, 'E': *************, 'a': {'B': [{'a': 'USDT', 'wb': '1657.6283113', 'cw': '1657.6283113', 'bc': '0'}], 'P': [{'s': 'SOLUSDT', 'pa': '0.32', 'ep': '188.42', 'cr': '0.********', 'up': '0.********', 'mt': 'cross', 'iw': '0', 'ps': 'LONG', 'ma': 'USDT', 'bep': '188.457684'}], 'm': 'ORDER'}}
2025-07-24 23:31:25,386 - INFO - [handle_order_update] - 配对订单更新: Pair 188.42->189.47, Side: BUY, Status: PARTIALLY_FILLED
2025-07-24 23:31:25,386 - INFO - [handle_websocket_message] - 收到账户更新: {'e': 'ACCOUNT_UPDATE', 'T': *************, 'E': *************, 'a': {'B': [{'a': 'USDT', 'wb': '1657.********', 'cw': '1657.********', 'bc': '0'}], 'P': [{'s': 'SOLUSDT', 'pa': '2.36', 'ep': '188.42', 'cr': '0.********', 'up': '0.********', 'mt': 'cross', 'iw': '0', 'ps': 'LONG', 'ma': 'USDT', 'bep': '188.457684'}], 'm': 'ORDER'}}
2025-07-24 23:31:25,386 - INFO - [handle_order_update] - 配对订单更新: Pair 188.42->189.47, Side: BUY, Status: FILLED
2025-07-24 23:31:25,387 - INFO - [handle_order_update] - 买单成交: 2.04 @ 188.42。准备挂配对卖单于 189.47
2025-07-24 23:31:25,387 - INFO - [place_order] - 尝试下单: sell LONG 2.0400 @ 189.4700
2025-07-24 23:31:27,201 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:29,651 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:30,274 - INFO - [place_order] - 下单成功: x-argm-1753371085387-9211
2025-07-24 23:31:30,274 - INFO - [_place_paired_sell_order] - 为配对 188.42->189.47 创建卖单成功。
2025-07-24 23:31:33,793 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:35,591 - INFO - [handle_order_update] - 配对订单更新: Pair 188.42->189.47, Side: SELL, Status: NEW
2025-07-24 23:31:36,373 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:38,724 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:41,930 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:52,998 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:55,335 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:31:57,506 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:00,124 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:02,555 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:07,087 - INFO - [monitor_account_drawdown] - 权益更新: 当前=1657.55, 峰值=1657.64, 回撤=0.01%
2025-07-24 23:32:10,676 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-24 23:32:10,676 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-24 23:32:10,677 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:21,144 - WARNING - [run] - WebSocket连接已关闭 (1006): 
2025-07-24 23:32:21,145 - INFO - [run] - 等待 5 秒后重连...
2025-07-24 23:32:28,651 - INFO - [run] - 已获取新的ListenKey
2025-07-24 23:32:28,652 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/PVMFls4fOf3OYvMFolRkByo3X13E79ls9Jt90ayqlbNYHf7dGv1Mg8mBYBWz6PDk
2025-07-24 23:32:28,845 - INFO - [run] - WebSocket 连接成功。
2025-07-24 23:32:28,846 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-24 23:32:32,512 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-24 23:32:32,518 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-24 23:32:32,518 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:35,097 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:37,557 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:39,699 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:41,764 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:43,877 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:45,945 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:48,164 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:50,462 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:52,828 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:54,870 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:57,496 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:32:59,598 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:33:01,887 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:33:03,967 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:33:06,067 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:33:08,622 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:33:09,567 - INFO - [monitor_account_drawdown] - 权益更新: 当前=1657.55, 峰值=1657.64, 回撤=0.01%
2025-07-24 23:33:11,006 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:33:13,593 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:33:15,660 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:33:23,998 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:33:26,678 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:33:28,702 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:33:31,217 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:33:36,326 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-24 23:33:36,326 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-24 23:33:36,326 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:33:38,474 - INFO - [place_new_buy_orders] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:33:48,866 - WARNING - [run] - WebSocket连接已关闭 (1006): 
2025-07-24 23:33:48,867 - INFO - [run] - 等待 5 秒后重连...
2025-07-24 23:33:56,370 - INFO - [run] - 已获取新的ListenKey
2025-07-24 23:33:56,370 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/PVMFls4fOf3OYvMFolRkByo3X13E79ls9Jt90ayqlbNYHf7dGv1Mg8mBYBWz6PDk
2025-07-24 23:33:56,790 - INFO - [run] - WebSocket 连接成功。
2025-07-24 23:33:56,790 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-24 23:33:59,539 - INFO - [close] - 正在关闭程序...
2025-07-24 23:33:59,539 - INFO - [cancel_all_open_orders] - 正在取消所有 9 个挂单...
2025-07-24 23:34:09,538 - WARNING - [close] - 关闭时取消挂单失败，但程序将继续关闭: 
2025-07-24 23:34:09,801 - INFO - [close] - 程序已关闭。
2025-07-24 23:42:51,377 - INFO - [main] - 启动 ARGM-V6.1 策略...
2025-07-24 23:42:51,377 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-24 23:42:51,385 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-24 23:42:51,386 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-24 23:42:51,388 - INFO - [setup] - 正在执行启动设置...
2025-07-24 23:42:51,389 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-24 23:42:51,389 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-24 23:42:51,396 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-24 23:42:51,486 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-24 23:42:51,487 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-24 23:42:52,083 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-24 23:42:52,084 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-24 23:42:52,413 - INFO - [test_connection] - 服务器时间获取成功: 1753371772366
2025-07-24 23:42:52,414 - INFO - [test_connection] - 正在加载市场数据...
2025-07-24 23:42:56,433 - INFO - [test_connection] - 市场数据加载成功
2025-07-24 23:42:56,433 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-24 23:42:56,433 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-24 23:42:57,706 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 188.92
2025-07-24 23:42:57,706 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-24 23:42:58,971 - INFO - [test_connection] - 余额获取成功，USDT余额: 1658.63974061
2025-07-24 23:42:58,972 - INFO - [test_connection] - API连接测试完成！
2025-07-24 23:42:58,972 - INFO - [setup] - 正在加载市场数据...
2025-07-24 23:43:03,076 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-24 23:43:04,341 - INFO - [set_hedge_mode] - 当前已是双向持仓模式
2025-07-24 23:43:05,603 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-24 23:43:11,684 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-24 23:43:11,684 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-24 23:43:12,946 - INFO - [setup] - 全局风控启动：初始权益 = 1657.55 USDT, 峰值权益 = 1657.55 USDT
2025-07-24 23:43:14,207 - INFO - [_initialize_grid_pairs] - 已成功初始化 19 个固定网格配对。
2025-07-24 23:43:14,207 - INFO - [_initialize_grid_pairs] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-24 23:43:14,207 - INFO - [_initialize_grid_pairs] - 根据 19 个网格配对，已计算出固定订单数量: 0.111111
2025-07-24 23:43:16,676 - INFO - [setup] - 初始化设置完成。
2025-07-24 23:43:16,677 - INFO - [setup] - 当前持仓: 多头=2.36, 空头=0.0
2025-07-24 23:43:16,677 - INFO - [cancel_all_open_orders] - 正在取消所有 9 个挂单...
2025-07-24 23:43:17,935 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-24 23:43:17,935 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-24 23:43:17,935 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/PVMFls4fOf3OYvMFolRkByo3X13E79ls9Jt90ayqlbNYHf7dGv1Mg8mBYBWz6PDk
2025-07-24 23:43:18,085 - INFO - [run] - WebSocket 连接成功。
2025-07-24 23:43:18,085 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-24 23:43:21,759 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-24 23:43:21,759 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-24 23:43:21,760 - INFO - [place_new_buy_orders] - 准备为 9 个空的网格配对创建买单，统一数量: 0.111111
2025-07-24 23:43:21,760 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 180.0000
2025-07-24 23:43:24,352 - INFO - [place_order] - 下单成功: x-argm-1753371801760-a79c
2025-07-24 23:43:24,353 - INFO - [place_new_buy_orders] - 为配对 180.0->181.05 创建买单成功。
2025-07-24 23:43:24,452 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 181.0500
2025-07-24 23:43:29,369 - INFO - [place_order] - 下单成功: x-argm-1753371804452-a8e6
2025-07-24 23:43:29,369 - INFO - [place_new_buy_orders] - 为配对 181.05->182.11 创建买单成功。
2025-07-24 23:43:29,473 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 182.1100
2025-07-24 23:43:34,332 - INFO - [place_order] - 下单成功: x-argm-1753371809473-39b0
2025-07-24 23:43:34,332 - INFO - [place_new_buy_orders] - 为配对 182.11->183.16 创建买单成功。
2025-07-24 23:43:34,434 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 183.1600
2025-07-24 23:43:39,306 - INFO - [place_order] - 下单成功: x-argm-1753371814434-bfe7
2025-07-24 23:43:39,306 - INFO - [place_new_buy_orders] - 为配对 183.16->184.21 创建买单成功。
2025-07-24 23:43:39,411 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 184.2100
2025-07-24 23:43:44,272 - INFO - [place_order] - 下单成功: x-argm-1753371819411-2c2f
2025-07-24 23:43:44,272 - INFO - [place_new_buy_orders] - 为配对 184.21->185.26 创建买单成功。
2025-07-24 23:43:44,377 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 185.2600
2025-07-24 23:43:49,277 - INFO - [place_order] - 下单成功: x-argm-1753371824377-7037
2025-07-24 23:43:49,277 - INFO - [place_new_buy_orders] - 为配对 185.26->186.32 创建买单成功。
2025-07-24 23:43:49,380 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 186.3200
2025-07-24 23:43:54,253 - INFO - [place_order] - 下单成功: x-argm-1753371829380-18fd
2025-07-24 23:43:54,253 - INFO - [place_new_buy_orders] - 为配对 186.32->187.37 创建买单成功。
2025-07-24 23:43:54,358 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 187.3700
2025-07-24 23:43:59,214 - INFO - [place_order] - 下单成功: x-argm-1753371834358-0400
2025-07-24 23:43:59,214 - INFO - [place_new_buy_orders] - 为配对 187.37->188.42 创建买单成功。
2025-07-24 23:43:59,322 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 188.4200
2025-07-24 23:44:04,187 - INFO - [place_order] - 下单成功: x-argm-*************-6def
2025-07-24 23:44:04,187 - INFO - [place_new_buy_orders] - 为配对 188.42->189.47 创建买单成功。
2025-07-24 23:44:06,564 - WARNING - [run] - WebSocket连接已关闭 (1006): 
2025-07-24 23:44:06,565 - INFO - [run] - 等待 5 秒后重连...
2025-07-24 23:44:16,441 - INFO - [run] - 已获取新的ListenKey
2025-07-24 23:44:16,441 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/PVMFls4fOf3OYvMFolRkByo3X13E79ls9Jt90ayqlbNYHf7dGv1Mg8mBYBWz6PDk
2025-07-24 23:44:16,566 - INFO - [run] - WebSocket 连接成功。
2025-07-24 23:44:16,567 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-24 23:44:20,251 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-24 23:44:20,251 - WARNING - [_reconcile_state] - 核对异常: 配对 188.42 状态为 BUY_PLACED，但交易所无此挂单 (x-argm-*************-6def)。重置为空闲。
2025-07-24 23:44:20,252 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-24 23:44:22,631 - INFO - [monitor_account_drawdown] - 权益更新: 当前=1657.55, 峰值=1657.55, 回撤=0.00%
2025-07-24 23:44:35,487 - INFO - [place_new_buy_orders] - 准备为 1 个空的网格配对创建买单，统一数量: 0.111111
2025-07-24 23:44:35,487 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 188.4200
2025-07-24 23:44:36,756 - INFO - [place_order] - 下单成功: x-argm-*************-455c
2025-07-24 23:44:36,757 - INFO - [place_new_buy_orders] - 为配对 188.42->189.47 创建买单成功。
2025-07-24 23:44:37,192 - INFO - [handle_order_update] - 配对订单更新: Pair 188.42->189.47, Side: BUY, Status: NEW
2025-07-24 23:44:37,285 - INFO - [handle_websocket_message] - 收到账户更新: {'e': 'ACCOUNT_UPDATE', 'T': *************, 'E': *************, 'a': {'B': [{'a': 'USDT', 'wb': '1657.********', 'cw': '1657.********', 'bc': '0'}], 'P': [{'s': 'SOLUSDT', 'pa': '2.58', 'ep': '188.42', 'cr': '0.********', 'up': '0', 'mt': 'cross', 'iw': '0', 'ps': 'LONG', 'ma': 'USDT', 'bep': '188.457684'}], 'm': 'ORDER'}}
2025-07-24 23:44:37,285 - INFO - [handle_order_update] - 配对订单更新: Pair 188.42->189.47, Side: BUY, Status: FILLED
2025-07-24 23:44:37,285 - INFO - [handle_order_update] - 买单成交: 0.11 @ 188.42。准备挂配对卖单于 189.47
2025-07-24 23:44:37,286 - INFO - [place_order] - 尝试下单: sell LONG 0.1100 @ 189.4700
2025-07-24 23:44:42,152 - INFO - [place_order] - 下单成功: x-argm-*************-977e
2025-07-24 23:44:42,152 - INFO - [_place_paired_sell_order] - 为配对 188.42->189.47 创建卖单成功。
2025-07-24 23:44:42,280 - INFO - [handle_order_update] - 配对订单更新: Pair 188.42->189.47, Side: SELL, Status: NEW
2025-07-24 23:45:27,737 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-24 23:45:27,737 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-24 23:45:30,126 - INFO - [monitor_account_drawdown] - 权益更新: 当前=1657.54, 峰值=1657.55, 回撤=0.00%
2025-07-24 23:46:31,726 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-24 23:46:31,726 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-24 23:46:34,120 - INFO - [monitor_account_drawdown] - 权益更新: 当前=1657.54, 峰值=1657.55, 回撤=0.00%
2025-07-24 23:47:00,879 - INFO - [close] - 正在关闭程序...
2025-07-24 23:47:00,879 - INFO - [cancel_all_open_orders] - 正在取消所有 9 个挂单...
2025-07-24 23:47:02,168 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-24 23:47:02,421 - INFO - [close] - 程序已关闭。
2025-07-25 00:04:50,527 - INFO - [main] - 启动 ARGM-V6.1 策略...
2025-07-25 00:04:50,528 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-25 00:04:50,539 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-25 00:04:50,539 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-25 00:04:50,541 - INFO - [setup] - 正在执行启动设置...
2025-07-25 00:04:50,542 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-25 00:04:50,542 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-25 00:04:50,549 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-25 00:04:50,550 - INFO - [run] - 状态报告器已启动。
2025-07-25 00:04:50,629 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-25 00:04:50,630 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-25 00:04:51,136 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-25 00:04:51,136 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-25 00:04:51,474 - INFO - [test_connection] - 服务器时间获取成功: 1753373091424
2025-07-25 00:04:51,474 - INFO - [test_connection] - 正在加载市场数据...
2025-07-25 00:04:55,492 - INFO - [test_connection] - 市场数据加载成功
2025-07-25 00:04:55,492 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-25 00:04:55,492 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-25 00:04:56,753 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 188.2
2025-07-25 00:04:56,754 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-25 00:04:58,028 - INFO - [test_connection] - 余额获取成功，USDT余额: 1657.13332022
2025-07-25 00:04:58,028 - INFO - [test_connection] - API连接测试完成！
2025-07-25 00:04:58,028 - INFO - [setup] - 正在加载市场数据...
2025-07-25 00:05:02,429 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-25 00:05:03,697 - INFO - [set_hedge_mode] - 当前已是双向持仓模式
2025-07-25 00:05:04,971 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-25 00:05:11,029 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-25 00:05:11,029 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-25 00:05:12,300 - INFO - [setup] - 全局风控启动：初始权益 = 1657.49 USDT, 峰值权益 = 1657.49 USDT
2025-07-25 00:05:13,564 - INFO - [_initialize_grid_pairs] - 已成功初始化 19 个固定网格配对。
2025-07-25 00:05:13,565 - INFO - [_initialize_grid_pairs] - 计划投入保证金: 20.0 USDT, 杠杆: 20x, 总名义价值: 400.0 USDT
2025-07-25 00:05:13,565 - INFO - [_initialize_grid_pairs] - 根据 19 个网格配对，已计算出固定订单数量: 0.111111
2025-07-25 00:05:16,018 - INFO - [setup] - 初始化设置完成。
2025-07-25 00:05:16,018 - INFO - [setup] - 当前持仓: 多头=2.58, 空头=0.0
2025-07-25 00:05:16,018 - INFO - [cancel_all_open_orders] - 正在取消所有 0 个挂单...
2025-07-25 00:05:17,279 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-25 00:05:17,279 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-25 00:05:17,280 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/PVMFls4fOf3OYvMFolRkByo3X13E79ls9Jt90ayqlbNYHf7dGv1Mg8mBYBWz6PDk
2025-07-25 00:05:17,488 - INFO - [run] - WebSocket 连接成功。
2025-07-25 00:05:17,489 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-25 00:05:21,165 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-25 00:05:21,166 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-25 00:05:21,166 - INFO - [place_new_buy_orders] - 准备为 8 个空的网格配对创建买单，统一数量: 0.111111
2025-07-25 00:05:21,166 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 180.0000
2025-07-25 00:05:23,641 - INFO - [place_order] - 下单成功: x-argm-1753373121166-4467
2025-07-25 00:05:23,641 - INFO - [place_new_buy_orders] - 为配对 180.0->181.05 创建买单成功。
2025-07-25 00:05:23,747 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 181.0500
2025-07-25 00:05:28,622 - INFO - [place_order] - 下单成功: x-argm-1753373123747-6265
2025-07-25 00:05:28,622 - INFO - [place_new_buy_orders] - 为配对 181.05->182.11 创建买单成功。
2025-07-25 00:05:28,734 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 182.1100
2025-07-25 00:05:33,597 - INFO - [place_order] - 下单成功: x-argm-1753373128734-4309
2025-07-25 00:05:33,597 - INFO - [place_new_buy_orders] - 为配对 182.11->183.16 创建买单成功。
2025-07-25 00:05:33,699 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 183.1600
2025-07-25 00:05:38,570 - INFO - [place_order] - 下单成功: x-argm-1753373133699-6d41
2025-07-25 00:05:38,570 - INFO - [place_new_buy_orders] - 为配对 183.16->184.21 创建买单成功。
2025-07-25 00:05:38,675 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 184.2100
2025-07-25 00:05:43,535 - INFO - [place_order] - 下单成功: x-argm-1753373138675-e0d0
2025-07-25 00:05:43,535 - INFO - [place_new_buy_orders] - 为配对 184.21->185.26 创建买单成功。
2025-07-25 00:05:43,640 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 185.2600
2025-07-25 00:05:48,509 - INFO - [place_order] - 下单成功: x-argm-1753373143640-dd17
2025-07-25 00:05:48,509 - INFO - [place_new_buy_orders] - 为配对 185.26->186.32 创建买单成功。
2025-07-25 00:05:48,613 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 186.3200
2025-07-25 00:05:50,558 - INFO - [display_status_report] - 
====== 策略状态报告 (2025-07-25 00:05:50) ======
策略状态: 运行中 | 交易对: SOL/USDT:USDT | 杠杆: 20x

--- 整体盈亏分析 ---
  初始权益: 1657.49 USDT
  当前权益: 1657.49 USDT
  浮动盈亏: +0.00 USDT (+0.00%)
  峰值权益: 1657.49 USDT
  当前回撤: 0.00%
  完成套利次数: 0 次

--- 仓位与风险 ---
  多头仓位: 2.5800 (485.72 USDT)
  当前保证金投入: 20.00 USDT
  下一复投目标: 20%

--- 网格状态分布 ---
  总配对数: 19
  已挂买单: 6 | 已挂卖单: 0 | 空闲格数: 13
==========================================

2025-07-25 00:05:53,478 - INFO - [place_order] - 下单成功: x-argm-1753373148613-6579
2025-07-25 00:05:53,478 - INFO - [place_new_buy_orders] - 为配对 186.32->187.37 创建买单成功。
2025-07-25 00:05:53,582 - INFO - [place_order] - 尝试下单: buy LONG 0.1100 @ 187.3700
2025-07-25 00:05:58,450 - INFO - [place_order] - 下单成功: x-argm-1753373153582-8262
2025-07-25 00:05:58,451 - INFO - [place_new_buy_orders] - 为配对 187.37->188.42 创建买单成功。
2025-07-25 00:06:03,485 - WARNING - [run] - WebSocket连接已关闭 (1006): 
2025-07-25 00:06:03,485 - INFO - [run] - 等待 5 秒后重连...
2025-07-25 00:06:13,350 - INFO - [run] - 已获取新的ListenKey
2025-07-25 00:06:13,350 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/PVMFls4fOf3OYvMFolRkByo3X13E79ls9Jt90ayqlbNYHf7dGv1Mg8mBYBWz6PDk
2025-07-25 00:06:13,484 - INFO - [run] - WebSocket 连接成功。
2025-07-25 00:06:13,485 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-25 00:06:17,168 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-25 00:06:17,168 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-25 00:06:19,551 - INFO - [monitor_account_drawdown] - 权益更新: 当前=1657.49, 峰值=1657.49, 回撤=0.00%
2025-07-25 00:06:50,552 - INFO - [display_status_report] - 
====== 策略状态报告 (2025-07-25 00:06:50) ======
策略状态: 运行中 | 交易对: SOL/USDT:USDT | 杠杆: 20x

--- 整体盈亏分析 ---
  初始权益: 1657.49 USDT
  当前权益: 1657.49 USDT
  浮动盈亏: +0.00 USDT (+0.00%)
  峰值权益: 1657.49 USDT
  当前回撤: 0.00%
  完成套利次数: 0 次

--- 仓位与风险 ---
  多头仓位: 2.5800 (485.78 USDT)
  当前保证金投入: 20.00 USDT
  下一复投目标: 20%

--- 网格状态分布 ---
  总配对数: 19
  已挂买单: 8 | 已挂卖单: 0 | 空闲格数: 11
==========================================

2025-07-25 00:06:55,788 - INFO - [close] - 正在关闭程序...
2025-07-25 00:06:55,788 - INFO - [cancel_all_open_orders] - 正在取消所有 8 个挂单...
2025-07-25 00:06:57,072 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-25 00:06:57,329 - INFO - [close] - 程序已关闭。
2025-07-25 01:47:07,190 - INFO - [main] - 启动 ARGM-V6.1 策略...
2025-07-25 01:47:07,191 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-25 01:47:07,198 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-25 01:47:07,198 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-25 01:47:07,200 - INFO - [setup] - 正在执行启动设置...
2025-07-25 01:47:07,200 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-25 01:47:07,200 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-25 01:47:07,208 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-25 01:47:07,209 - INFO - [run] - 状态报告器已启动。
2025-07-25 01:47:07,287 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-25 01:47:07,288 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-25 01:47:07,779 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-25 01:47:07,780 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-25 01:47:08,102 - INFO - [test_connection] - 服务器时间获取成功: 1753379228048
2025-07-25 01:47:08,103 - INFO - [test_connection] - 正在加载市场数据...
2025-07-25 01:47:12,187 - INFO - [test_connection] - 市场数据加载成功
2025-07-25 01:47:12,188 - INFO - [test_connection] - 交易对 BTC/USDT:USDT 验证成功
2025-07-25 01:47:12,188 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-25 01:47:13,449 - INFO - [test_connection] - Ticker 获取成功: BTC/USDT:USDT @ 119168.3
2025-07-25 01:47:13,449 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-25 01:47:14,717 - INFO - [test_connection] - 余额获取成功，USDT余额: 1658.44912022
2025-07-25 01:47:14,717 - INFO - [test_connection] - API连接测试完成！
2025-07-25 01:47:14,717 - INFO - [setup] - 正在加载市场数据...
2025-07-25 01:47:18,795 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.001, 'price': 0.1, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-25 01:47:18,796 - INFO - [setup] - 已从交易所动态获取最小订单名义价值: 100.0 USDT
2025-07-25 01:47:20,050 - INFO - [set_hedge_mode] - 当前已是双向持仓模式
2025-07-25 01:47:21,313 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-25 01:47:27,378 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-25 01:47:27,378 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-25 01:47:28,640 - INFO - [setup] - 全局风控启动：初始权益 = 1657.49 USDT, 峰值权益 = 1657.49 USDT
2025-07-25 01:47:29,906 - INFO - [_initialize_grid_pairs] - 已成功初始化 39 个固定网格配对。
2025-07-25 01:47:29,906 - CRITICAL - [_initialize_grid_pairs] - !!! 配置致命错误：根据总网格数计算，每个买单的平均名义价值 (51.28 USDT) 低于交易所要求的最小名义价值 (100.00 USDT)。
2025-07-25 01:47:29,906 - CRITICAL - [_initialize_grid_pairs] - 请增加 'total_investment' (当前 100.0) 或减少网格数量 'grid_count' 来解决此问题。程序将退出。
2025-07-25 01:47:29,907 - INFO - [close] - 正在关闭程序...
2025-07-25 01:47:29,907 - INFO - [cancel_all_open_orders] - 正在取消所有 0 个挂单...
2025-07-25 01:47:32,371 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-25 01:47:32,634 - INFO - [close] - 程序已关闭。
2025-07-25 01:47:32,668 - ERROR - [default_exception_handler] - Task exception was never retrieved
future: <Task finished name='Task-1' coro=<main() done, defined at c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3.py:1608> exception=SystemExit(1)>
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3.py", line 1634, in <module>
    asyncio.run(main())
  File "C:\Python312\Lib\asyncio\runners.py", line 193, in run
    with Runner(debug=debug, loop_factory=loop_factory) as runner:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\runners.py", line 62, in __exit__
    self.close()
  File "C:\Python312\Lib\asyncio\runners.py", line 70, in close
    _cancel_all_tasks(loop)
  File "C:\Python312\Lib\asyncio\runners.py", line 205, in _cancel_all_tasks
    loop.run_until_complete(tasks.gather(*to_cancel, return_exceptions=True))
  File "C:\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
  File "C:\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
  File "C:\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
  File "C:\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3.py", line 1620, in main
    await asyncio.gather(main_task, reporter_task)
  File "C:\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
  File "C:\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
  File "C:\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
  File "C:\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3.py", line 485, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3.py", line 470, in setup
    self._initialize_grid_pairs() # [新增] 初始化配对网格
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3.py", line 835, in _initialize_grid_pairs
    sys.exit(1)
SystemExit: 1
2025-07-25 01:47:42,715 - INFO - [main] - 启动 ARGM-V6.1 策略...
2025-07-25 01:47:42,716 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-25 01:47:42,723 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-25 01:47:42,724 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-25 01:47:42,726 - INFO - [setup] - 正在执行启动设置...
2025-07-25 01:47:42,726 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-25 01:47:42,726 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-25 01:47:42,734 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-25 01:47:42,735 - INFO - [run] - 状态报告器已启动。
2025-07-25 01:47:42,815 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-25 01:47:42,816 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-25 01:47:43,299 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-25 01:47:43,299 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-25 01:47:43,626 - INFO - [test_connection] - 服务器时间获取成功: 1753379263572
2025-07-25 01:47:43,627 - INFO - [test_connection] - 正在加载市场数据...
2025-07-25 01:47:47,688 - INFO - [test_connection] - 市场数据加载成功
2025-07-25 01:47:47,689 - INFO - [test_connection] - 交易对 BTC/USDT:USDT 验证成功
2025-07-25 01:47:47,689 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-25 01:47:48,966 - INFO - [test_connection] - Ticker 获取成功: BTC/USDT:USDT @ 119176.9
2025-07-25 01:47:48,966 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-25 01:47:50,219 - INFO - [test_connection] - 余额获取成功，USDT余额: 1658.54523765
2025-07-25 01:47:50,219 - INFO - [test_connection] - API连接测试完成！
2025-07-25 01:47:50,219 - INFO - [setup] - 正在加载市场数据...
2025-07-25 01:47:54,289 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.001, 'price': 0.1, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-25 01:47:54,289 - INFO - [setup] - 已从交易所动态获取最小订单名义价值: 100.0 USDT
2025-07-25 01:47:55,552 - INFO - [set_hedge_mode] - 当前已是双向持仓模式
2025-07-25 01:47:56,828 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-25 01:48:02,893 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-25 01:48:02,893 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-25 01:48:04,154 - INFO - [setup] - 全局风控启动：初始权益 = 1657.49 USDT, 峰值权益 = 1657.49 USDT
2025-07-25 01:48:05,421 - INFO - [_initialize_grid_pairs] - 已成功初始化 39 个固定网格配对。
2025-07-25 01:48:05,421 - INFO - [_initialize_grid_pairs] - 计划投入保证金: 500.0 USDT, 杠杆: 20x, 总名义价值: 10000.0 USDT
2025-07-25 01:48:05,421 - INFO - [_initialize_grid_pairs] - 根据 39 个网格配对，已计算出固定订单数量: 0.002169
2025-07-25 01:48:07,891 - INFO - [setup] - 初始化设置完成。
2025-07-25 01:48:07,892 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-25 01:48:07,892 - INFO - [cancel_all_open_orders] - 正在取消所有 0 个挂单...
2025-07-25 01:48:09,140 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-25 01:48:09,140 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-25 01:48:09,140 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/7l2EqA2oj0WgT7YAPc1uWXEq9aM0v6iJAARoYRma5rl3S042ZKdrusDX64GhEDfp
2025-07-25 01:48:09,285 - INFO - [run] - WebSocket 连接成功。
2025-07-25 01:48:09,286 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['btcusdt@bookTicker'], 'id': 1}
2025-07-25 01:48:13,117 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-07-25 01:48:13,118 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-07-25 01:48:13,118 - INFO - [place_new_buy_orders] - 准备为 27 个空的网格配对创建买单，统一数量: 0.002169
2025-07-25 01:48:13,118 - INFO - [place_order] - 尝试下单: buy LONG 0.0020 @ 115800.0000
2025-07-25 01:48:15,597 - INFO - [place_order] - 下单成功: x-argm-1753379293118-c95c
2025-07-25 01:48:15,598 - INFO - [place_new_buy_orders] - 为配对 115800.0->115928.2 创建买单成功。
2025-07-25 01:48:15,703 - INFO - [place_order] - 尝试下单: buy LONG 0.0020 @ 115928.2000
2025-07-25 01:48:19,690 - INFO - [close] - 正在关闭程序...
2025-07-25 01:48:19,690 - INFO - [cancel_all_open_orders] - 正在取消所有 1 个挂单...
2025-07-25 01:48:25,366 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-25 01:48:25,623 - INFO - [close] - 程序已关闭。
2025-08-03 23:41:43,872 - INFO - [main] - 启动 ARGM-V6.1 策略...
2025-08-03 23:41:43,879 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-08-03 23:41:43,879 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-08-03 23:41:43,881 - INFO - [setup] - 正在执行启动设置...
2025-08-03 23:41:43,881 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-08-03 23:41:43,881 - INFO - [test_connection] - 正在测试基础网络连接...
2025-08-03 23:41:43,887 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-08-03 23:41:43,892 - INFO - [run] - 状态报告器已启动。
2025-08-03 23:42:04,939 - ERROR - [test_connection] - 端点测试失败 https://fapi.binance.com/fapi/v1/ping: Cannot connect to host fapi.binance.com:443 ssl:default [None]
2025-08-03 23:42:04,939 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/time
2025-08-03 23:42:25,983 - INFO - [close] - 正在关闭程序...
2025-08-03 23:42:26,247 - INFO - [close] - 程序已关闭。
2025-08-03 23:42:43,873 - INFO - [main] - 启动 ARGM-V6.1 策略...
2025-08-03 23:42:43,873 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-08-03 23:42:43,880 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-08-03 23:42:43,881 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-08-03 23:42:43,882 - INFO - [setup] - 正在执行启动设置...
2025-08-03 23:42:43,882 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-08-03 23:42:43,882 - INFO - [test_connection] - 正在测试基础网络连接...
2025-08-03 23:42:43,889 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-08-03 23:42:43,890 - INFO - [run] - 状态报告器已启动。
2025-08-03 23:42:44,108 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-08-03 23:42:44,108 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-08-03 23:42:44,770 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-08-03 23:42:44,770 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-08-03 23:42:45,242 - INFO - [test_connection] - 服务器时间获取成功: 1754235765158
2025-08-03 23:42:45,243 - INFO - [test_connection] - 正在加载市场数据...
2025-08-03 23:42:49,294 - INFO - [test_connection] - 市场数据加载成功
2025-08-03 23:42:49,294 - INFO - [test_connection] - 交易对 BTC/USDT:USDT 验证成功
2025-08-03 23:42:49,294 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-08-03 23:42:50,568 - INFO - [test_connection] - Ticker 获取成功: BTC/USDT:USDT @ 113900.3
2025-08-03 23:42:50,568 - INFO - [test_connection] - 正在测试账户余额获取...
2025-08-03 23:42:51,830 - INFO - [test_connection] - 余额获取成功，USDT余额: 1201.69283696
2025-08-03 23:42:51,830 - INFO - [test_connection] - API连接测试完成！
2025-08-03 23:42:51,830 - INFO - [setup] - 正在加载市场数据...
2025-08-03 23:42:55,984 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.001, 'price': 0.1, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-08-03 23:42:55,984 - INFO - [setup] - 已从交易所动态获取最小订单名义价值: 100.0 USDT
2025-08-03 23:42:57,267 - INFO - [set_hedge_mode] - 当前已是双向持仓模式
2025-08-03 23:42:58,534 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-08-03 23:43:04,622 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-08-03 23:43:04,622 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-08-03 23:43:05,912 - INFO - [setup] - 全局风控启动：初始权益 = 1201.69 USDT, 峰值权益 = 1201.69 USDT
2025-08-03 23:43:07,184 - INFO - [_initialize_grid_pairs] - 已成功初始化 39 个固定网格配对。
2025-08-03 23:43:07,184 - CRITICAL - [_initialize_grid_pairs] - !!! 配置致命错误：根据总网格数计算，每个买单的平均名义价值 (51.28 USDT) 低于交易所要求的最小名义价值 (100.00 USDT)。
2025-08-03 23:43:07,184 - CRITICAL - [_initialize_grid_pairs] - 请增加 'total_investment' (当前 100.0) 或减少网格数量 'grid_count' 来解决此问题。程序将退出。
2025-08-03 23:43:07,184 - INFO - [close] - 正在关闭程序...
2025-08-03 23:43:07,185 - INFO - [cancel_all_open_orders] - 正在取消所有 0 个挂单...
2025-08-03 23:43:09,687 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-08-03 23:43:09,951 - INFO - [close] - 程序已关闭。
2025-08-03 23:43:09,996 - ERROR - [default_exception_handler] - Task exception was never retrieved
future: <Task finished name='Task-1' coro=<main() done, defined at c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3.py:1857> exception=SystemExit(1)>
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3.py", line 1883, in <module>
    asyncio.run(main())
  File "C:\Python312\Lib\asyncio\runners.py", line 193, in run
    with Runner(debug=debug, loop_factory=loop_factory) as runner:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\runners.py", line 62, in __exit__
    self.close()
  File "C:\Python312\Lib\asyncio\runners.py", line 70, in close
    _cancel_all_tasks(loop)
  File "C:\Python312\Lib\asyncio\runners.py", line 205, in _cancel_all_tasks
    loop.run_until_complete(tasks.gather(*to_cancel, return_exceptions=True))
  File "C:\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
  File "C:\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
  File "C:\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
  File "C:\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3.py", line 1869, in main
    await asyncio.gather(main_task, reporter_task)
  File "C:\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
  File "C:\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
  File "C:\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
  File "C:\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3.py", line 502, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3.py", line 478, in setup
    self._initialize_grid_pairs() # [新增] 初始化配对网格
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN_V3.py", line 851, in _initialize_grid_pairs
    sys.exit(1)
SystemExit: 1
2025-08-03 23:43:18,858 - INFO - [main] - 启动 ARGM-V6.1 策略...
2025-08-03 23:43:18,858 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-08-03 23:43:18,865 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-08-03 23:43:18,866 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-08-03 23:43:18,867 - INFO - [setup] - 正在执行启动设置...
2025-08-03 23:43:18,867 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-08-03 23:43:18,867 - INFO - [test_connection] - 正在测试基础网络连接...
2025-08-03 23:43:18,874 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-08-03 23:43:18,875 - INFO - [run] - 状态报告器已启动。
2025-08-03 23:43:19,099 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-08-03 23:43:19,100 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-08-03 23:43:19,736 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-08-03 23:43:19,736 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-08-03 23:43:20,196 - INFO - [test_connection] - 服务器时间获取成功: 1754235800113
2025-08-03 23:43:20,197 - INFO - [test_connection] - 正在加载市场数据...
2025-08-03 23:43:24,265 - INFO - [test_connection] - 市场数据加载成功
2025-08-03 23:43:24,265 - INFO - [test_connection] - 交易对 BTC/USDT:USDT 验证成功
2025-08-03 23:43:24,266 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-08-03 23:43:25,533 - INFO - [test_connection] - Ticker 获取成功: BTC/USDT:USDT @ 113904.3
2025-08-03 23:43:25,533 - INFO - [test_connection] - 正在测试账户余额获取...
2025-08-03 23:43:26,826 - INFO - [test_connection] - 余额获取成功，USDT余额: 1201.69283696
2025-08-03 23:43:26,826 - INFO - [test_connection] - API连接测试完成！
2025-08-03 23:43:26,826 - INFO - [setup] - 正在加载市场数据...
2025-08-03 23:43:30,989 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.001, 'price': 0.1, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-08-03 23:43:30,989 - INFO - [setup] - 已从交易所动态获取最小订单名义价值: 100.0 USDT
2025-08-03 23:43:32,278 - INFO - [set_hedge_mode] - 当前已是双向持仓模式
2025-08-03 23:43:33,543 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-08-03 23:43:39,631 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-08-03 23:43:39,632 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-08-03 23:43:40,916 - INFO - [setup] - 全局风控启动：初始权益 = 1201.69 USDT, 峰值权益 = 1201.69 USDT
2025-08-03 23:43:42,189 - INFO - [_initialize_grid_pairs] - 已成功初始化 9 个固定网格配对。
2025-08-03 23:43:42,189 - INFO - [_initialize_grid_pairs] - 计划投入保证金: 100.0 USDT, 杠杆: 20x, 总名义价值: 2000.0 USDT
2025-08-03 23:43:42,189 - INFO - [_initialize_grid_pairs] - 将采用动态订单数量计算，确保总风险敞口始终控制在预设范围内
2025-08-03 23:43:44,663 - INFO - [setup] - === 专业交易参数配置 ===
2025-08-03 23:43:44,664 - INFO - [setup] - PostOnly (开仓): 启用
2025-08-03 23:43:44,664 - INFO - [setup] - ReduceOnly (平仓): 启用
2025-08-03 23:43:44,664 - INFO - [setup] - PostOnly 重试次数: 2
2025-08-03 23:43:44,664 - INFO - [setup] - 价格调整幅度: 0.0100%
2025-08-03 23:43:44,664 - INFO - [setup] - ========================
2025-08-03 23:43:44,664 - INFO - [setup] - 初始化设置完成。
2025-08-03 23:43:44,664 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-08-03 23:43:44,664 - INFO - [cancel_all_open_orders] - 正在取消所有 0 个挂单...
2025-08-03 23:43:45,939 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-08-03 23:43:45,939 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-08-03 23:43:45,939 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/j3l3h69WdzHiVzTc5mci1LzyMpoojFonkJk4jzirTEtRJaA6FW4Y2dG5dcYT7SCO
2025-08-03 23:43:46,192 - INFO - [run] - WebSocket 连接成功。
2025-08-03 23:43:46,193 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['btcusdt@bookTicker'], 'id': 1}
2025-08-03 23:43:49,892 - INFO - [_reconcile_state] - --- 开始状态核对与修复 ---
2025-08-03 23:43:49,892 - INFO - [_reconcile_state] - --- 状态核对与修复完成 ---
2025-08-03 23:43:49,893 - WARNING - [execute_strategy] - 价格 113890.85 突破固定区间 [115800.0, 120800.0]。触发动作: pause
2025-08-03 23:43:49,893 - INFO - [execute_strategy] - 策略将暂停 3600 秒。
2025-08-03 23:43:49,893 - INFO - [cancel_all_open_orders] - 正在取消所有 0 个挂单...
2025-08-03 23:43:52,365 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-08-03 23:44:14,583 - INFO - [close] - 正在关闭程序...
2025-08-03 23:44:14,583 - INFO - [cancel_all_open_orders] - 正在取消所有 0 个挂单...
2025-08-03 23:44:15,998 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-08-03 23:44:16,261 - INFO - [close] - 程序已关闭。
