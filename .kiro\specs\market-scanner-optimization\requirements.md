# 加密货币市场扫描器优化需求文档

## 介绍

当前的加密货币市场扫描器（0622v2.py）是一个功能丰富的品种发现工具，但存在复杂度过高、信号过多、与实际交易策略匹配度不足等问题。本优化项目旨在基于第一性原理，简化扫描器逻辑，专注于为三种核心交易策略（趋势策略、震荡策略、配对策略）提供精准的交易信号。

## 需求

### 需求1：核心指标简化与优化

**用户故事：** 作为量化交易者，我希望扫描器只关注最有效的核心指标，以便减少噪音并提高信号质量。

#### 验收标准

1. WHEN 计算强度因子 THEN 系统 SHALL 只使用经过验证的核心指标（动量、RSI、成交量比率、相对强度）
2. WHEN 指标权重分配 THEN 系统 SHALL 基于历史回测结果优化权重分配
3. WHEN 数据获取 THEN 系统 SHALL 移除低价值指标（如资金费率、持仓变化）以提高扫描速度
4. WHEN Z-Score计算 THEN 系统 SHALL 使用滚动窗口而非全市场静态标准化
5. WHEN 指标异常处理 THEN 系统 SHALL 对缺失或异常数据使用合理的默认值

### 需求2：策略导向的信号分类

**用户故事：** 作为交易策略执行者，我希望扫描器直接输出适合不同策略的标的清单，以便快速做出交易决策。

#### 验收标准

1. WHEN 趋势策略筛选 THEN 系统 SHALL 识别高动量、突破关键阻力位的品种
2. WHEN 震荡策略筛选 THEN 系统 SHALL 识别在区间内波动、波动率适中的品种
3. WHEN 配对策略筛选 THEN 系统 SHALL 识别强势主流币和弱势山寨币的配对组合
4. WHEN 信号输出 THEN 系统 SHALL 为每个策略提供不超过5个最优标的
5. WHEN 策略匹配度评分 THEN 系统 SHALL 为每个标的计算其适合特定策略的评分

### 需求3：趋势策略信号优化

**用户故事：** 作为趋势跟踪交易者，我希望获得明确的趋势方向和入场时机信号，以便执行DCA抄底、趋势跟踪和区间突破策略。

#### 验收标准

1. WHEN DCA抄底信号 THEN 系统 SHALL 识别超跌但基本面强劲的主流币种
2. WHEN 趋势跟踪信号 THEN 系统 SHALL 识别处于明确上升趋势且动量强劲的品种
3. WHEN 区间突破信号 THEN 系统 SHALL 识别即将或刚刚突破关键阻力位的品种
4. WHEN 趋势强度评估 THEN 系统 SHALL 使用ADX和价格动量综合评估趋势强度
5. WHEN 入场时机判断 THEN 系统 SHALL 结合成交量和价格行为确定最佳入场点

### 需求4：震荡策略信号优化

**用户故事：** 作为网格交易者，我希望识别适合网格策略的品种和参数设置，以便在震荡市场中稳定获利。

#### 验收标准

1. WHEN 网格适用性评估 THEN 系统 SHALL 识别波动率稳定、无明显趋势的品种
2. WHEN 网格参数建议 THEN 系统 SHALL 基于历史波动率建议网格间距和区间设置
3. WHEN 震荡区间识别 THEN 系统 SHALL 识别明确的支撑阻力区间
4. WHEN 流动性评估 THEN 系统 SHALL 确保推荐品种有足够的流动性支持网格交易
5. WHEN 风险评估 THEN 系统 SHALL 评估品种的突破风险和最大回撤可能性

### 需求5：配对策略信号优化

**用户故事：** 作为配对交易者，我希望获得明确的多空配对建议，以便执行做多强势主流币、做空弱势山寨币的策略。

#### 验收标准

1. WHEN 强势主流币筛选 THEN 系统 SHALL 从BTC、ETH、BNB等主流币中选择相对强势品种
2. WHEN 弱势山寨币筛选 THEN 系统 SHALL 从市值较小的山寨币中选择相对弱势品种
3. WHEN 配对组合生成 THEN 系统 SHALL 生成风险平衡的多空配对组合
4. WHEN 相关性分析 THEN 系统 SHALL 确保配对品种相关性适中，避免同向风险
5. WHEN 配对比例建议 THEN 系统 SHALL 基于波动率差异建议合理的仓位比例

### 需求6：性能优化与简化

**用户故事：** 作为系统运维者，我希望扫描器运行高效、资源占用少，以便长期稳定运行。

#### 验收标准

1. WHEN 数据获取优化 THEN 系统 SHALL 减少API调用次数，使用批量获取和缓存机制
2. WHEN 计算复杂度降低 THEN 系统 SHALL 移除不必要的复杂计算和多重循环
3. WHEN 内存使用优化 THEN 系统 SHALL 及时释放不需要的数据，避免内存泄漏
4. WHEN 扫描频率优化 THEN 系统 SHALL 根据市场活跃度动态调整扫描频率
5. WHEN 错误处理简化 THEN 系统 SHALL 使用简单有效的错误处理机制

### 需求7：输出界面简化

**用户故事：** 作为交易决策者，我希望获得简洁明了的输出信息，以便快速理解市场机会。

#### 验收标准

1. WHEN 信息展示 THEN 系统 SHALL 只显示最关键的交易信号，避免信息过载
2. WHEN 策略分类展示 THEN 系统 SHALL 按三种策略分别展示推荐标的
3. WHEN 关键指标显示 THEN 系统 SHALL 只显示与交易决策直接相关的指标
4. WHEN 实时更新 THEN 系统 SHALL 提供清晰的时间戳和更新状态
5. WHEN 历史记录 THEN 系统 SHALL 保存关键信号的历史记录用于回测验证

### 需求8：配置管理简化

**用户故事：** 作为策略配置者，我希望有简单直观的配置选项，以便快速调整扫描器参数。

#### 验收标准

1. WHEN 参数配置 THEN 系统 SHALL 提供最少但最有效的配置选项
2. WHEN 默认设置 THEN 系统 SHALL 使用经过优化的默认参数，开箱即用
3. WHEN 策略切换 THEN 系统 SHALL 支持快速切换不同策略的扫描模式
4. WHEN 参数验证 THEN 系统 SHALL 自动验证参数合理性并提供建议
5. WHEN 配置持久化 THEN 系统 SHALL 自动保存用户的配置偏好