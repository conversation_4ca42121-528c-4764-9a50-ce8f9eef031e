# Fix for pandas_ta compatibility with newer NumPy versions
import numpy as np
if not hasattr(np, 'NaN'):
    np.NaN = np.nan

import sys # 导入 sys 模块
# 修复 aiodns 在 Windows 上的兼容性问题
if sys.platform == 'win32':
    import asyncio
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())


import ccxt.pro # 异步需要
import asyncio   # 异步需要
import pandas as pd
import pandas_ta as ta
import time
from datetime import datetime
import requests # For Feishu Webhook
import json     # For Feishu Webhook
from rich.console import Console
from rich.table import Table
from rich.live import Live
from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn

# === CONFIGURATION ===
CONFIG = {
    'timeframes': ['4h', '1d'],
    'timeframe_weights': [0.6, 0.4],
    'lookback': 20,
    'top_n': 10,
    # [V3 Simplification] 因子权重调整为三个核心维度
    'factor_weights': {
        'rel_strength_z': 0.5,   # 动量 (Momentum)
        'volume_24h_z':   0.3,   # 流动性 (Liquidity)
        'funding_rate_z': -0.2,  # 情绪/资金面 (Sentiment/Funding) - 直接使用负权重
    },
    'scan_interval_seconds': 1800, # Scan every 10 minutes
    'feishu_webhook_url': 'https://open.feishu.cn/open-apis/bot/v2/hook/f9a9508a-2940-4cf8-b629-8e2da21b59e9',
}
# =====================

# 1. 初始化 - 将在主函数中进行
# exchange = ccxt.binance({
#     'enableRateLimit': True,
#     'options': {'defaultType': 'future'}
# })
console = Console() # For rich printing

# 2. 获取所有永续合约品种 (异步版本)
async def fetch_perpetual_symbols(exchange):
    try:
        markets = await exchange.load_markets()
        # 更严格的筛选条件：
        # 1. 是永续合约 (swap: True)
        # 2. 处于活跃状态 (active: True)
        # 3. 是U本位合约 (settle: 'USDT')
        # 4. 交易对的计价货币是USDT (quote: 'USDT')
        perpetuals = [
            s for s, m in markets.items()
            if m.get('swap') and m.get('active') and m.get('settle') == 'USDT' and m.get('quote') == 'USDT'
        ]
        console.log(f"Found [bold green]{len(perpetuals)}[/bold green] active USDT-margined perpetual contracts.")
        return perpetuals
    except Exception as e:
        console.log(f"[bold red]Error fetching markets:[/bold red] {e}")
        return []

# 3. 指标计算引擎 (异步 + 修复前瞻性偏差)
async def calculate_strength_factors(exchange, symbol, timeframes, lookback, btc_data_cache):
    all_timeframe_results = []
    for timeframe in timeframes:
        try:
            # 异步获取数据
            ohlcv = await exchange.fetch_ohlcv(symbol, timeframe, limit=lookback + 100)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            # 确保有足够的数据来进行 lookback-2 的计算
            if len(df) < lookback + 27: continue

            # --- [新功能] 增加状态指标计算 ---
            # [V3 Simplification] 使用 pandas_ta 计算 RSI, 并额外计算EMA20用于后续筛选
            df.ta.rsi(append=True) # Default length is 14
            df.ta.ema(length=20, append=True)
            df.ta.adx(length=lookback, append=True)
            df.ta.bbands(length=lookback, append=True)

            rsi_col = 'RSI_14'
            ema_col = 'EMA_20'
            adx_col = f'ADX_{lookback}'
            bbw_col = f'BBB_{lookback}_2.0'

            # [偏差修复] 使用 iloc[-2] 获取已收盘K线的数据
            adx = df[adx_col].iloc[-2] if adx_col in df.columns and pd.notna(df[adx_col].iloc[-2]) else 0
            bbw = df[bbw_col].iloc[-2] if bbw_col in df.columns and pd.notna(df[bbw_col].iloc[-2]) else 0
            # [V3 Simplification] 获取RSI和EMA20
            rsi = df[rsi_col].iloc[-2] if rsi_col in df.columns and pd.notna(df[rsi_col].iloc[-2]) else 50
            ema_20 = df[ema_col].iloc[-2] if ema_col in df.columns and pd.notna(df[ema_col].iloc[-2]) else 0

            # [偏差修复] 突破状态计算基于已收盘K线
            recent_high = df['high'].rolling(window=lookback).max().iloc[-2] # lookback 窗口的高点 (不包含当前K线)
            recent_low = df['low'].rolling(window=lookback).min().iloc[-2]
            is_breaking_up = df['close'].iloc[-2] > recent_high
            is_breaking_down = df['close'].iloc[-2] < recent_low
            breakout_status = 'Up' if is_breaking_up else ('Down' if is_breaking_down else 'None')
            
            # --- 获取衍生品数据 (异步) ---
            oi_change = 0.0
            try:
                oi_history = await exchange.fetch_open_interest_history(symbol, timeframe, limit=lookback)
                oi_df = pd.DataFrame(oi_history)
                oi_df['openInterest'] = pd.to_numeric(oi_df['openInterestValue'])
                if not oi_df.empty and len(oi_df) > 1 and oi_df['openInterest'].iloc[0] > 0:
                    oi_change = (oi_df['openInterest'].iloc[-1] / oi_df['openInterest'].iloc[0] - 1) * 100
            except Exception: pass

            funding_rate = 0.0
            try:
                funding_data = await exchange.fetch_funding_rate(symbol)
                funding_rate = funding_data.get('fundingRate', 0.0)
            except Exception: pass
            
            # --- 获取24h成交额 (异步) ---
            volume_24h = 0.0
            try:
                ticker_data = await exchange.fetch_ticker(symbol)
                # 使用 quoteVolume，它是以计价货币（如USDT）计量的成交额，更具可比性
                volume_24h = ticker_data.get('quoteVolume', 0.0)
            except Exception:
                pass # 获取失败则为0
            
            # --- 核心指标计算 (偏差修复版) ---
            # [偏差修复] 动量 Momentum (Rate of Change)
            momentum = (df['close'].iloc[-2] / df['close'].iloc[-lookback-2] - 1) * 100 if df['close'].iloc[-lookback-2] > 0 else 0

            # [V3 DEPRECATED] 手动RSI计算已移除，改用 pandas_ta

            # [偏差修复] 成交量比率 (使用过去5根和lookback根已收盘K线)
            avg_vol_short = df['volume'].iloc[-6:-1].mean()
            avg_vol_long = df['volume'].iloc[-lookback-1:-1].mean()
            volume_ratio = avg_vol_short / avg_vol_long if avg_vol_long > 0 else 1

            # 相对强度 (vs BTC)
            rel_strength = momentum
            try:
                # [并发优化] 使用缓存的BTC数据
                btc_df = btc_data_cache.get(timeframe)
                if btc_df is not None:
                    btc_momentum = (btc_df['close'].iloc[-2] / btc_df['close'].iloc[-lookback-2] - 1) * 100
                    rel_strength = momentum - btc_momentum
            except Exception: pass

            timeframe_result = {
                'momentum': momentum, 'rsi': rsi, 'volume_ratio': volume_ratio,
                'rel_strength': rel_strength, 'funding_rate': funding_rate, 'oi_change': oi_change,
                # 新增的状态因子
                'adx': adx,
                'bbw': bbw,
                'breakout_status': breakout_status,
                'volume_24h': volume_24h,
                'close': df['close'].iloc[-2], # V3 新增，用于后续策略计算
                'ema_20': ema_20, # V3 新增，用于后续策略计算
                'recent_high': recent_high, # V3 新增，用于网格策略
                'recent_low': recent_low,   # V3 新增，用于网格策略
            }
            all_timeframe_results.append(timeframe_result)

        except Exception as e:
            # console.log(f"Error processing {symbol} on {timeframe}: {e}", style="yellow")
            continue
    
    if not all_timeframe_results: return None

    # --- 多时间周期加权平均 ---
    final_result = {}
    weights = CONFIG['timeframe_weights']
    for key in all_timeframe_results[0].keys():
        if key == 'breakout_status':
            final_result[key] = all_timeframe_results[0][key] # 只取主时间周期的突破状态
            continue
        # volume_24h 不需要加权平均，因为它不是基于时间周期的，只取第一个即可
        if key == 'volume_24h':
            final_result[key] = all_timeframe_results[0].get(key, 0.0)
            continue

        values = [res[key] for res in all_timeframe_results]
        current_weights = weights[:len(values)]
        if sum(current_weights) > 0:
            final_result[key] = np.average(values, weights=current_weights)
        else: # Fallback if weights are misconfigured or only one timeframe succeeded
            final_result[key] = np.mean(values)
            
    final_result['symbol'] = symbol
    return final_result

# 4. 全市场扫描和Z-Score归一化 (异步并发版)
async def full_market_scan(exchange):
    all_symbols = await fetch_perpetual_symbols(exchange)
    if not all_symbols:
        return pd.DataFrame(), None

    # [V3.1 Optimization] 先获取所有ticker的成交额，筛选出Top 50
    console.log("Fetching all tickers to determine top 50 by volume...")
    try:
        # fetch_tickers() 获取所有可用市场的ticker信息
        all_tickers = await exchange.fetch_tickers()
        volume_data = []
        # 从全市场中过滤出我们需要的USDT永续合约
        for symbol in all_symbols:
            if symbol in all_tickers and all_tickers[symbol].get('quoteVolume') is not None:
                volume_data.append({
                    'symbol': symbol,
                    'quoteVolume': all_tickers[symbol]['quoteVolume']
                })

        if not volume_data:
            console.log("[bold red]Could not fetch volume data for any symbol.[/bold red]")
            return pd.DataFrame(), None

        # 按成交额排序并选出前50
        volume_df = pd.DataFrame(volume_data)
        top_50_df = volume_df.sort_values('quoteVolume', ascending=False).head(50)
        symbols = top_50_df['symbol'].tolist()

        console.log(f"Identified Top 50 symbols by 24h volume. Now scanning [bold green]{len(symbols)}[/bold green] symbols.")
    except Exception as e:
        console.log(f"[bold red]Error fetching tickers or filtering top 50: {e}[/bold red]")
        return pd.DataFrame(), None
    
    # [并发优化] 提前一次性获取所有时间周期的BTC数据
    console.log("Fetching BTC data for all timeframes...")
    btc_data_cache = {}
    try:
        btc_fetch_tasks = [exchange.fetch_ohlcv('BTC/USDT', tf, limit=CONFIG['lookback'] + 100) for tf in CONFIG['timeframes']]
        all_btc_ohlcv = await asyncio.gather(*btc_fetch_tasks)
        for i, tf in enumerate(CONFIG['timeframes']):
            btc_df = pd.DataFrame(all_btc_ohlcv[i], columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            if len(btc_df) >= CONFIG['lookback'] + 27:
                 btc_data_cache[tf] = btc_df
    except Exception as e:
        console.log(f"[bold red]Could not fetch BTC base data, relative strength will be affected: {e}[/bold red]")


    # [并发优化] 使用 asyncio.gather 和 rich Progress Bar
    tasks = [
        calculate_strength_factors(exchange, symbol, timeframes=CONFIG['timeframes'], lookback=CONFIG['lookback'], btc_data_cache=btc_data_cache)
        for symbol in symbols
    ]
    
    results = []  # Initialize results list
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
    ) as progress:
        scan_task = progress.add_task("[cyan]Scanning market...", total=len(tasks))
        # 使用 asyncio.as_completed 在任务完成时更新进度条
        for future in asyncio.as_completed(tasks):
            try:
                result = await future
                if result:
                    results.append(result)
            except Exception as e:
                # 在单个任务失败时记录错误，但不中断整个扫描
                # console.log(f"Error processing a symbol: {e}", style="yellow")
                pass
            finally:
                progress.update(scan_task, advance=1)

    if not results:
        console.log("[bold red]Scan resulted in no data.[/bold red]")
        return pd.DataFrame(), None

    df = pd.DataFrame(results).set_index('symbol')
    
    # --- [V3 Robustness] Winsorization 缩尾处理 ---
    # 防止极端异常值扭曲Z-Score
    factors_to_winsorize = ['momentum', 'rsi', 'volume_ratio', 'rel_strength', 'funding_rate', 'oi_change', 'volume_24h']
    for factor in factors_to_winsorize:
        if factor in df.columns:
            # 替换超出97.5%和2.5%分位数的值
            p_low = df[factor].quantile(0.025)
            p_high = df[factor].quantile(0.975)
            df[factor] = np.clip(df[factor], p_low, p_high)

    # --- Z-Score 归一化 (V3 精简版) ---
    fw = CONFIG['factor_weights']
    
    # 计算需要Z-Score的因子
    for factor_with_z in fw.keys():
        # 'rel_strength_z' -> 'rel_strength'
        base_factor = factor_with_z.replace('_z', '')
        if base_factor in df.columns:
             df[factor_with_z] = (df[base_factor] - df[base_factor].mean()) / df[base_factor].std()
    
    # --- 计算最终强度分 (V3 精简版) ---
    df['strength'] = 0
    for factor_z, weight in fw.items():
        if factor_z in df.columns:
            df['strength'] += weight * df[factor_z]
    
    df = df.sort_values('strength', ascending=False).reset_index()
    df = df.dropna(subset=['strength']) # 移除计算失败的行

    # --- [新功能] 计算与BTC的相关性 (V2.0 优化版) ---
    correlation_matrix = None # 初始化为None

    try:
        console.log("Calculating correlation matrix...")
        # 准备用于计算相关性的币种列表 (此列表现在已经很干净了)
        symbols_for_corr = df['symbol'].tolist()
        
        # 确保BTC/USDT在列表中以便作为基准，如果不在则手动添加
        base_btc_symbol = 'BTC/USDT'
        if base_btc_symbol not in symbols_for_corr:
            symbols_for_corr.insert(0, base_btc_symbol)

        # [V3 Robustness] 将相关性计算固定在 1d 周期，90天回看期
        corr_timeframe = '1d'
        corr_lookback = 90
        
        # [V3 Robustness] 重构相关性计算逻辑，以处理不等长的时间序列数据
        all_close_series = {}
        console.log(f"Fetching historical data for {len(symbols_for_corr)} assets for correlation calc...")
        
        # [并发优化] 并发获取所有相关性计算所需的数据
        ohlcv_tasks = [exchange.fetch_ohlcv(sym, corr_timeframe, limit=corr_lookback) for sym in symbols_for_corr]
        results = await asyncio.gather(*ohlcv_tasks, return_exceptions=True)

        for sym, ohlcv_result in zip(symbols_for_corr, results):
             if isinstance(ohlcv_result, list) and len(ohlcv_result) > 1:
                # 使用DataFrame来正确处理时间戳
                temp_df = pd.DataFrame(ohlcv_result, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                temp_df['timestamp'] = pd.to_datetime(temp_df['timestamp'], unit='ms')
                temp_df.set_index('timestamp', inplace=True)
                all_close_series[sym] = temp_df['close']
        
        if len(all_close_series) >= 2:
            # 合并所有Series，Pandas会根据时间戳自动对齐，并填充NaN
            close_prices_df = pd.concat(all_close_series, axis=1)
            
            # 使用前向填充(forward fill)处理因数据频率不同或数据点缺失造成的NaN
            close_prices_df.ffill(inplace=True)
            
            # 计算价格变化率，并删除完全是NaN的行 (通常是第一行)
            returns_df = close_prices_df.pct_change().dropna(how='all')

            # 删除数据不足、完全是NaN的列 (有些币种可能历史太短)
            returns_df.dropna(axis=1, how='all', inplace=True)
            
            if returns_df.shape[1] >= 2:
                correlation_matrix = returns_df.corr()
            else:
                console.log("[bold yellow]Not enough comparable asset data to build correlation matrix.[/bold yellow]")
        else:
            console.log("[bold yellow]Not enough historical data to build correlation matrix.[/bold yellow]")
        
    except Exception as e:
        console.log(f"[bold red]Correlation calculation failed:[/bold red] {e}")
 
    return df, correlation_matrix

# 5. 结果可视化展示 (V3: 策略候选池)
def display_trend_pool(df):
   table = Table(title="🏆 趋势策略池 (Trend Pool)", style="default", title_style="bold blue")
   table.add_column("Direction", justify="center")
   table.add_column("Symbol", justify="left", style="cyan")
   table.add_column("Strength", justify="right")
   table.add_column("ADX", justify="right")
   table.add_column("Notes", justify="left")

   longs = df[(df['strength'] > 1.0) & (df['adx'] > 25)].sort_values('strength', ascending=False)
   shorts = df[(df['strength'] < -1.0) & (df['adx'] > 25)].sort_values('strength', ascending=True)

   for _, row in longs.iterrows():
       notes = []
       if abs(row['close'] - row['ema_20']) / row['close'] < 0.02:
           notes.append("Pullback")
       if row['volume_ratio'] > 1.5:
           notes.append("Volume Up")
       table.add_row("[bold green]LONG[/bold green]", row['symbol'], f"{row['strength']:.2f}", f"{row['adx']:.2f}", ", ".join(notes))

   if not longs.empty and not shorts.empty:
        table.add_row("---", "---", "---", "---", "---")

   for _, row in shorts.iterrows():
       table.add_row("[bold red]SHORT[/bold red]", row['symbol'], f"{row['strength']:.2f}", f"{row['adx']:.2f}", "")

   if not longs.empty or not shorts.empty:
       console.print(table)

def display_range_pool(df):
   bbw_p20 = df['bbw'].quantile(0.20)
   bbw_p50 = df['bbw'].quantile(0.50)
   
   range_candidates = df[
       (df['strength'].between(-0.5, 0.5)) &
       (df['adx'] < 20) &
       (df['bbw'].between(bbw_p20, bbw_p50))
   ].sort_values('bbw', ascending=True)

   if range_candidates.empty:
       return

   table = Table(title="🔀 震荡/网格策略池 (Range/Grid Pool)", style="default", title_style="bold purple")
   table.add_column("Symbol", justify="left", style="cyan")
   table.add_column("Strength", justify="right")
   table.add_column("BBW", justify="right", style="yellow")
   table.add_column("Suggested Range", justify="center")

   for _, row in range_candidates.head(10).iterrows():
        table.add_row(
            row['symbol'],
            f"{row['strength']:.2f}",
            f"{row['bbw']:.4f}",
            f"{row['recent_low']:.4f} - {row['recent_high']:.4f}"
        )
   console.print(table)

def display_pairs_pool(df, correlation_matrix):
    if correlation_matrix is None or df.empty:
        return

    # [V3.2 Optimization] 直接从Top 50池中选出最强和最弱的作为候选，不再使用绝对阈值
    long_candidates = df.sort_values('strength', ascending=False).head(5)
    short_candidates = df.sort_values('strength', ascending=True).head(5)

    if long_candidates.empty or short_candidates.empty:
        return

    pairs = []
    # 取strength最强的5个作为多头，最弱的5个作为空头
    for _, long_row in long_candidates.iterrows():
        for _, short_row in short_candidates.iterrows():
            long_sym = long_row['symbol']
            short_sym = short_row['symbol']
            if long_sym == short_sym: continue # 避免自己和自己配对

            if long_sym in correlation_matrix.index and short_sym in correlation_matrix.columns:
                corr = correlation_matrix.loc[long_sym, short_sym]
                if corr < 0.3:
                    strength_diff = long_row['strength'] - short_row['strength']
                    pairs.append((long_sym, long_row['strength'], short_sym, short_row['strength'], strength_diff, corr))

    if not pairs:
        return

    # Sort by strength difference
    pairs.sort(key=lambda x: x[4], reverse=True)

    table = Table(title="⚖️ 配对交易策略池 (Pairs Trading Pool)", style="default", title_style="bold yellow")
    table.add_column("Long Candidate", justify="left")
    table.add_column("Short Candidate", justify="left")
    table.add_column("Strength Diff.", justify="right")
    table.add_column("Correlation", justify="right")

    for long_s, long_str, short_s, short_str, str_diff, corr_val in pairs:
        table.add_row(
            f"[green]{long_s}[/] ({long_str:.2f})",
            f"[red]{short_s}[/] ({short_str:.2f})",
            f"[bold]{str_diff:.2f}[/bold]",
            f"{corr_val:.3f}"
        )
    console.print(table)

# 5b. 推送到飞书
async def send_feishu_notification(full_df, correlation_matrix):
    """构建飞书消息卡片并发送通知 (V2.1: 异步安全版)"""
    webhook_url = CONFIG.get('feishu_webhook_url')
    if not webhook_url or 'YOUR_WEBHOOK_KEY' in webhook_url:
        console.log("Feishu Webhook URL not configured, skipping notification.")
        return

    def text_div(content):
        """辅助函数：创建lark_md格式的文本块"""
        return {"tag": "div", "text": {"content": content, "tag": "lark_md"}}

    def column_set(columns, flex_mode="none", background_style="default"):
        """辅助函数：创建分栏布局"""
        return {
            "tag": "column_set",
            "flex_mode": flex_mode,
            "background_style": background_style,
            "columns": [
                {
                    "tag": "column",
                    "width": col.get("width", "auto"),
                    "weight": col.get("weight", 0),
                    "elements": [text_div(col["content"])]
                } for col in columns
            ]
        }

    elements = []

    # --- 1. 趋势策略池 ---
    elements.append(text_div("🏆 **趋势策略池 (Trend Pool)**"))
    longs = full_df[(full_df['strength'] > 1.0) & (full_df['adx'] > 25)].sort_values('strength', ascending=False)
    shorts = full_df[(full_df['strength'] < -1.0) & (full_df['adx'] > 25)].sort_values('strength', ascending=True)

    if not longs.empty or not shorts.empty:
        elements.append(column_set([
            {"content": "**方向**", "width": "weighted", "weight": 1},
            {"content": "**交易对**", "width": "weighted", "weight": 2},
            {"content": "**强度**", "width": "weighted", "weight": 1},
            {"content": "**ADX**", "width": "weighted", "weight": 1}
        ], background_style="grey"))
        for _, row in longs.iterrows():
            elements.append(column_set([
                {"content": "🟢 看多", "width": "weighted", "weight": 1},
                {"content": f"{row['symbol']}", "width": "weighted", "weight": 2},
                {"content": f"{row['strength']:.2f}", "width": "weighted", "weight": 1},
                {"content": f"{row['adx']:.2f}", "width": "weighted", "weight": 1}
            ]))
        for _, row in shorts.iterrows():
            elements.append(column_set([
                {"content": "🔴 看空", "width": "weighted", "weight": 1},
                {"content": f"{row['symbol']}", "width": "weighted", "weight": 2},
                {"content": f"{row['strength']:.2f}", "width": "weighted", "weight": 1},
                {"content": f"{row['adx']:.2f}", "width": "weighted", "weight": 1}
            ]))
    else:
        elements.append(text_div("当前市场无明显趋势信号"))
    elements.append({"tag": "hr"})

    # --- 2. 震荡/网格策略池 ---
    elements.append(text_div("🔀 **震荡/网格策略池 (Range/Grid Pool)**"))
    bbw_p20 = full_df['bbw'].quantile(0.20)
    bbw_p50 = full_df['bbw'].quantile(0.50)
    range_candidates = full_df[
       (full_df['strength'].between(-0.5, 0.5)) & (full_df['adx'] < 20) & (full_df['bbw'].between(bbw_p20, bbw_p50))
    ].sort_values('bbw', ascending=True)
    if not range_candidates.empty:
        elements.append(column_set([
            {"content": "**交易对**", "width": "weighted", "weight": 2},
            {"content": "**建议区间**", "width": "weighted", "weight": 3},
            {"content": "**BBW**", "width": "weighted", "weight": 1}
        ], background_style="grey"))
        for _, row in range_candidates.iterrows():
            elements.append(column_set([
                {"content": f"{row['symbol']}", "width": "weighted", "weight": 2},
                {"content": f"{row['recent_low']:.4f} - {row['recent_high']:.4f}", "width": "weighted", "weight": 3},
                {"content": f"{row['bbw']:.4f}", "width": "weighted", "weight": 1}
            ]))
    else:
        elements.append(text_div("无合适的震荡策略候选"))
    elements.append({"tag": "hr"})

    # --- 3. 配对交易策略池 ---
    elements.append(text_div("⚖️ **配对交易策略池 (Pairs Trading Pool)**"))
    pairs_found = False
    if correlation_matrix is not None:
        long_candidates = full_df.sort_values('strength', ascending=False).head(5)
        short_candidates = full_df.sort_values('strength', ascending=True).head(5)
        pairs = []
        if not long_candidates.empty and not short_candidates.empty:
            for _, long_row in long_candidates.iterrows():
                for _, short_row in short_candidates.iterrows():
                    if long_row['symbol'] == short_row['symbol']: continue
                    long_sym, short_sym = long_row['symbol'], short_row['symbol']
                    if long_sym in correlation_matrix.index and short_sym in correlation_matrix.columns:
                        corr = correlation_matrix.loc[long_sym, short_sym]
                        if corr < 0.3:
                            strength_diff = long_row['strength'] - short_row['strength']
                            pairs.append((long_sym, short_sym, strength_diff, corr, long_row['strength'], short_row['strength']))
            if pairs:
                pairs_found = True
                pairs.sort(key=lambda x: x[2], reverse=True)
                elements.append(column_set([
                    {"content": "**多头 (强度)**", "width": "weighted", "weight": 2},
                    {"content": "**空头 (强度)**", "width": "weighted", "weight": 2},
                    {"content": "**强度差**", "width": "weighted", "weight": 1},
                    {"content": "**相关性**", "width": "weighted", "weight": 1}
                ], background_style="grey"))
                for long_s, short_s, str_diff, corr_val, long_str, short_str in pairs:
                    elements.append(column_set([
                        {"content": f"{long_s} ({long_str:.2f})", "width": "weighted", "weight": 2},
                        {"content": f"{short_s} ({short_str:.2f})", "width": "weighted", "weight": 2},
                        {"content": f"**{str_diff:.2f}**", "width": "weighted", "weight": 1},
                        {"content": f"{corr_val:.3f}", "width": "weighted", "weight": 1}
                    ]))
    if not pairs_found:
        elements.append(text_div("无合适的配对交易机会"))

    # --- 构建最终卡片消息 ---
    card_message = {
        "msg_type": "interactive",
        "card": {
            "config": {"wide_screen_mode": True},
            "header": {
                "title": {"content": f"📈 市场机会扫描仪 V3 - {datetime.now().strftime('%H:%M:%S')}", "tag": "plain_text"},
                "template": "blue"
            },
            "elements": elements
        }
    }

    # --- 发送请求 ---
    def _blocking_post():
        """将阻塞的requests调用封装在一个函数中，以便在线程池中运行"""
        try:
            response = requests.post(webhook_url, headers={'Content-Type': 'application/json'}, data=json.dumps(card_message))
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return e

    loop = asyncio.get_running_loop()
    # 在默认的线程池执行器中运行阻塞的IO操作，避免阻塞事件循环
    result = await loop.run_in_executor(None, _blocking_post)

    if isinstance(result, Exception):
        console.log(f"[bold red]Error sending Feishu notification:[/bold red] {result}")
    elif result.get("StatusCode") != 0:
        console.log(f"[bold red]Failed to send Feishu notification:[/bold red] {result.get('StatusMessage')}")
    else:
        console.log("Feishu notification sent successfully.")


def display_strategy_pools(full_df, correlation_matrix):
    """ 主函数，调用所有策略池的展示函数 """
    console.print("\n[bold]===== 市场机会扫描仪 (V3) =====[/bold]")
    display_trend_pool(full_df)
    display_range_pool(full_df)
    display_pairs_pool(full_df, correlation_matrix)

# 6. 主监控循环 (异步版)
async def run_realtime_monitor():
    # [并发优化] 创建一个可重用的异步交易所实例
    exchange = ccxt.pro.binance({
        'enableRateLimit': True,
        'options': {'defaultType': 'future'}
    })
    try:
        while True:
            console.rule(f"[bold yellow]Starting New Scan at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            full_df, correlation_matrix = await full_market_scan(exchange)
            
            if not full_df.empty:
                # [V3] 显示全新的策略候选池
                display_strategy_pools(full_df, correlation_matrix)
                
                # [V3.3] 发送飞书通知 (现在是异步的)
                await send_feishu_notification(full_df, correlation_matrix)
                
                # 保存完整结果
                filename = f"market_strength_{datetime.now().strftime('%Y%m%d_%H%M')}.csv"
                full_df.to_csv(filename)
                console.log(f"Full market scan saved to [underline]{filename}[/underline]")
            
            console.log(f"Scan complete. Waiting for {CONFIG['scan_interval_seconds']} seconds...")
            await asyncio.sleep(CONFIG['scan_interval_seconds'])
    finally:
        # [并发优化] 确保在程序退出时关闭交易所连接
        await exchange.close()


# 运行系统
if __name__ == "__main__":
    try:
        asyncio.run(run_realtime_monitor())
    except KeyboardInterrupt:
        console.log("Monitor stopped by user.")