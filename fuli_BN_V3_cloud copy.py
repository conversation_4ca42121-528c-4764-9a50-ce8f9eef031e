# ------------------------------------------------------------------------------------
# ARGM-V6.0 "Singularity" - 自适应趋势网格复投策略 (已升级支持固定区间)
# ------------------------------------------------------------------------------------
# 作者: Lens & AI
# 日期: 2025-07-12
# 描述: 本策略是基于第一性原理的自适应网格系统。
#       它将网格的位置、范围、密度及重绘决策统一到一个核心函数中，
#       在捕捉均值回归利润的同时，集成了趋势过滤、智能风险控制和利润复投机制。
# 版本历史:
# V6.1: (本次升级)
#       - 新增 "fixed_grid" 固定区间网格策略模式。
#       - 重构配置结构，引入 strategy_type 开关。
#       - 实现固定区间的算术网格生成与价格突破处理逻辑。
# V6.0: 整合入专业的异步交易框架，替换原有的做市逻辑。
# ------------------------------------------------------------------------------------

import asyncio
import websockets
import json
import logging
import time
import math
import os
import sys
import uuid
import pandas as pd
import numpy as np
from collections import namedtuple
import aiohttp
import ccxt.async_support as ccxt_async


# ====================================================================================
# --- 策略配置 (所有可调参数集中于此) ---
# ====================================================================================
CONFIG = {
    # --- 账户与连接配置 ---
    "api_key": "USlBuHx9zNwxXFtecEQZDOXXxbHhKMIViMeRaELIyjOHGCcdkoESgmxpu4bWi47c",      # ❗❗❗ 替换为你的 API Key
    "api_secret": "lxNiaI54wlWAyxpZ5oNajJ84MoQYodnakDWfoQiuONNTtU9YKsKS14tRwgBi7eUt",  # ❗❗❗ 替换为你的 API Secret
    "use_proxy": True,
    "proxy_url": "http://127.0.0.1:7897",

    # --- 策略核心参数 ---
    "coin_name": "BTC",
    "contract_type": "USDT",
    "leverage": 20,
    # "initial_value": 20, # [废弃] 旧的每格名义价值，由下面的 total_investment 替代

    # --- [重构] 策略通用配置 ---
    "strategy_type": "fixed_grid", # [新增] 策略类型: "fixed_grid" 或 "adaptive_grid"
    
    "strategy_config": {
        # [核心改造] 计划总投入 (保证金, USDT)，策略将根据此金额和杠杆计算出总名义价值，再进行下单
        "total_investment": 500.0,
        
        # --- [新增] 固定区间网格配置 (当 strategy_type = "fixed_grid") ---
        "fixed_grid": {
            "range_high": 120800.0,   # 宏观区间上限
            "range_low": 115800.0,    # 宏观区间下限
            "grid_count": 40,      # 网格数量
            # [新增] 价格突破区间后的行为: "warn", "pause", "stop"
            "on_breach_action": "pause", 
            "pause_duration_on_breach_seconds": 3600, # 暂停时长（秒）
        },

        # --- ARGM-V6.0 自适应网格配置 (当 strategy_type = "adaptive_grid") ---
        "adaptive_grid": {
            "ema_trend_period": 200,      # 宏观趋势判定周期
            "ema_anchor_period": 96,       # 网格中枢EMA周期, 也用作布林带计算周期
            "bb_std_dev_multiplier": 2.0,  # 布林带标准差倍数
            "grid_spacing_pct": 0.005,     # 每个网格的间距百分比 (0.5%)
            "repaint_overlap_ratio": 0.6,  # 新旧网格重合度低于此值则重绘
            "kline_timeframe": '1h',       # 用于计算指标的K线周期
            # 趋势方向手动覆盖开关: "AUTO", "LONG_ONLY", "SHORT_ONLY"
            "trend_override": "LONG_ONLY",
            "strategy_execution_interval_seconds": 10.0,  # 策略执行最小间隔(秒)，防止过度频繁执行
        },
    },

    # --- [新增] 智能风险与复投配置 ---
    "risk_management": {
        # [核心改造] 总投资额是主要的风控来源，硬性仓位上限已移除
        # "position_limit_contracts": 0.5,
        # [新增] 账户最大回撤止损
        "max_account_drawdown_pct": 0.99,  # 15%
        "drawdown_check_interval_seconds": 60,
        # [移除] 单笔最小名义价值将从交易所动态获取
        # "min_notional_value_usd": 5.0,
    },

    # --- [新增] 浮盈加仓配置 ---
    "reinvestment": {
        "enabled": True,             # 是否启用复投功能
        "profit_threshold_pct": 0.20,  # 20% - 每当净利润率达到此值的整数倍时，触发复投
        "reinvest_ratio": 0.50       # 50% - 将该阶段利润的50%追加到总投资中
    },

    # --- 系统运行参数 (基本保持不变) ---
    "system": {
        "sync_interval_seconds": 60,
        "timeout_seconds": 30,
        "ticker_update_interval_ms": 500, # 适当放宽，避免过于频繁调整
        "max_connection_retries": 5,
        "connection_retry_delay": 5,
        "state_sync_on_error": True,
        "websocket_ping_interval": 20,
        "websocket_timeout": 60
    }
}


# ====================================================================================
# --- 日志配置 (保持不变) ---
# ====================================================================================
if not os.path.exists("log"):
    os.makedirs("log")
script_name = os.path.splitext(os.path.basename(__file__))[0]
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - [%(funcName)s] - %(message)s",
    handlers=[
        logging.FileHandler(f"log/{script_name}.log", encoding="utf-8"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger()


# ====================================================================================
# --- 主策略类 ---
# ====================================================================================
class ARGMStrategyBot:
    # [新增] 定义GridState数据结构
    GridState = namedtuple('GridState', ['grids', 'center', 'low', 'high', 'should_repaint'])

    def __init__(self, config):
        """初始化策略实例"""
        self.config = config
        self.lock = asyncio.Lock()
        self.order_semaphore = asyncio.Semaphore(3)  # 限制并发下单数量

        self.exchange = self._initialize_exchange()
        # 修复：使用正确的期货交易对格式
        self.symbol = f"{config['coin_name']}/USDT:USDT"  # 期货交易对格式
        self.market = None
        # [新增] 从交易所动态获取的最小订单名义价值
        self.min_notional_value = 5.0 # 默认/回退值
        
        contract_type = self.config['contract_type'].upper()
        if contract_type in ['USDT', 'USDC']:
            self.websocket_base_url = "wss://fstream.binance.com"
        else:
            logger.error(f"不支持的合约类型: {contract_type}"); sys.exit(1)
        logger.info(f"已设置WebSocket URL: {self.websocket_base_url}")

        # --- 实时数据状态 ---
        self.best_bid_price = 0.0
        self.best_ask_price = 0.0
        self.last_price = 0.0
        self.price_series = pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

        # --- 核心策略状态 ---
        self.long_position_size = 0.0
        self.short_position_size = 0.0
        self.net_inventory = 0.0
        self.open_orders = {}
        self.current_grids = [] # [重构] 即将被 self.grid_pairs 替代
        self.grid_pairs = [] # [新增] 配对挂单模式的核心数据结构
        self.order_quantity_per_grid = 0.0 # [新增] 在初始化时计算的、固定的每格订单数量

        # [新增] 状态报告与历史记录
        self.reporter = None
        self.trade_history = []
        self.completed_cycles = 0
        self.equity_curve = []

        # [新增] 复投状态
        self.reinvestment_cfg = self.config.get('reinvestment', {})
        if self.reinvestment_cfg.get('enabled'):
            self.next_reinvest_profit_target = self.reinvestment_cfg.get('profit_threshold_pct', 0.2)
        else:
            self.next_reinvest_profit_target = 0

        # [新增] 全局风控状态
        self.initial_equity = 0.0
        self.peak_equity = 0.0
        self.is_permanently_stopped = False
        
        self._last_grid_price = 0.0  # 用于网格重绘频率控制
        
        # [新增] 暂停交易状态
        self.is_paused = False
        self.pause_end_time = 0

        # --- 系统控制变量 ---
        self.listen_key = None
        self.last_state_sync_time = 0
        self.last_ticker_time = 0
        self.last_strategy_execution_time = 0  # 新增：策略执行频率控制
        self.is_shutting_down = False

    def _initialize_exchange(self):
        """初始化并返回CCXT交易所实例"""
        if "YOUR_API_KEY" in self.config['api_key'] or "YOUR_API_SECRET" in self.config['api_secret']:
            logger.error("API密钥未配置，请在CONFIG中设置后再运行。")
            sys.exit(1)

        exchange_class = getattr(ccxt_async, self.config.get('exchange_id', 'binanceusdm'))

        exchange_config = {
            'apiKey': self.config['api_key'],
            'secret': self.config['api_secret'],
            'options': {
                'defaultType': 'future',
                'adjustForTimeDifference': True,
                'recvWindow': 10000,
                'warnOnFetchOpenOrdersWithoutSymbol': False,
                'createMarketBuyOrderRequiresPrice': False
            },
            'timeout': 120000,
            'enableRateLimit': True,
            'rateLimit': 1200,
            'headers': {
                'User-Agent': 'Mozilla/5.0'
            }
        }

        if self.config.get('use_proxy') and self.config.get('proxy_url'):
            exchange_config['aiohttp_proxy'] = self.config['proxy_url']
            logger.info(f"已通过 aiohttp_proxy 设置代理: {self.config['proxy_url']}")

        exchange = exchange_class(exchange_config)
        logger.info("交易所实例创建成功")
        return exchange
    
    async def test_connection(self):
        """测试网络连接和API可用性"""
        max_retries = 5  # 增加重试次数
        retry_delay = 3
        
        for attempt in range(max_retries):
            try:
                logger.info(f"正在测试API连接... (尝试 {attempt + 1}/{max_retries})")
                
                # 1. 首先测试基础网络连接
                logger.info("正在测试基础网络连接...")
                proxy = self.config['proxy_url'] if self.config.get('use_proxy') else None
                
                # 使用更长的超时时间和更好的错误处理
                connector = aiohttp.TCPConnector(
                    limit=100,
                    limit_per_host=30,
                    ttl_dns_cache=300,
                    use_dns_cache=True,
                    keepalive_timeout=30,
                    enable_cleanup_closed=True
                )
                
                timeout = aiohttp.ClientTimeout(
                    total=60,  # 总超时时间
                    connect=30,  # 连接超时
                    sock_read=30  # 读取超时
                )
                
                async with aiohttp.ClientSession(
                    connector=connector,
                    timeout=timeout,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                ) as session:
                    # 测试多个端点以确保连接稳定
                    test_urls = [
                        "https://fapi.binance.com/fapi/v1/ping",
                        "https://fapi.binance.com/fapi/v1/time"
                    ]
                    
                    for test_url in test_urls:
                        try:
                            logger.info(f"测试端点: {test_url}")
                            async with session.get(test_url, proxy=proxy) as response:
                                if response.status == 200:
                                    result = await response.json()
                                    logger.info(f"端点测试成功: {test_url} - {result}")
                                    break
                                else:
                                    logger.warning(f"端点返回状态码: {response.status}")
                        except Exception as url_error:
                            logger.error(f"端点测试失败 {test_url}: {url_error}")
                            if test_url == test_urls[-1]:  # 最后一个URL也失败
                                 raise url_error
                            continue
                
                # 2. 测试CCXT交易所连接
                logger.info("正在测试CCXT交易所连接...")
                try:
                    # 使用更简单的ping方法
                    ping_result = await asyncio.wait_for(
                        self.exchange.publicGetPing(), timeout=45
                    )
                    logger.info(f"CCXT Ping 成功: {ping_result}")
                except Exception as ping_error:
                    logger.warning(f"CCXT Ping失败，尝试备用方法: {ping_error}")
                    # 尝试直接调用时间API
                    try:
                        time_result = await asyncio.wait_for(
                            self.exchange.publicGetTime(), timeout=45
                        )
                        logger.info(f"时间API调用成功: {time_result}")
                    except Exception as time_error:
                        logger.error(f"时间API也失败: {time_error}")
                        raise time_error
                
                # 3. 测试服务器时间获取
                logger.info("正在测试服务器时间获取...")
                try:
                    server_time = await asyncio.wait_for(
                        self.exchange.fetch_time(), timeout=45
                    )
                    logger.info(f"服务器时间获取成功: {server_time}")
                except Exception as time_error:
                    logger.warning(f"服务器时间获取失败: {time_error}")
                    # 继续尝试其他测试

                # 4. 加载市场数据
                logger.info("正在加载市场数据...")
                try:
                    await asyncio.wait_for(self.exchange.load_markets(), timeout=60)
                    logger.info("市场数据加载成功")

                    # 验证交易对是否存在
                    if self.exchange.markets and self.symbol in self.exchange.markets:
                        logger.info(f"交易对 {self.symbol} 验证成功")
                    else:
                        logger.warning(f"交易对 {self.symbol} 不存在，尝试查找替代交易对...")
                        if self.exchange.markets:
                            # 尝试多种交易对格式
                            possible_symbols = [
                                f"{self.config['coin_name']}/USDT:USDT",  # 期货格式
                                f"{self.config['coin_name']}/USDT",       # 现货格式
                                f"{self.config['coin_name']}USDT",        # 简化格式
                            ]
                            
                            found_symbol = None
                            for test_symbol in possible_symbols:
                                if test_symbol in self.exchange.markets:
                                    found_symbol = test_symbol
                                    break
                            
                            if found_symbol:
                                logger.info(f"找到可用交易对: {found_symbol}")
                                self.symbol = found_symbol
                            else:
                                # 显示可用的USDT交易对
                                available_symbols = [s for s in list(self.exchange.markets.keys())[:10] if 'USDT' in s]
                                logger.warning("可用的USDT交易对示例:")
                                for sym in available_symbols:
                                     logger.warning(f"  - {sym}")
                                logger.error(f"无法找到 {self.config['coin_name']} 相关的交易对")
                        else:
                            logger.error("市场数据为空")

                except Exception as market_error:
                    logger.warning(f"市场数据加载失败: {market_error}")
                    logger.warning("市场数据加载失败，但继续测试...")

                # 5. 测试ticker数据获取
                logger.info("正在测试ticker数据获取...")
                try:
                    ticker = await asyncio.wait_for(
                        self.exchange.fetch_ticker(self.symbol), timeout=45
                    )
                    logger.info(f"Ticker 获取成功: {ticker['symbol']} @ {ticker['last']}")
                except Exception as ticker_error:
                    logger.warning(f"Ticker获取失败: {ticker_error}")
                    logger.warning("Ticker获取失败，但这可能是交易对问题，继续测试...")
                
                # 6. 测试账户余额获取 (需要API密钥)
                logger.info("正在测试账户余额获取...")
                try:
                    balance = await asyncio.wait_for(
                        self.exchange.fetch_balance(), timeout=45
                    )
                    usdt_balance = balance.get('USDT', {}).get('total', 'N/A')
                    logger.info(f"余额获取成功，USDT余额: {usdt_balance}")
                    
                    # 检查余额是否足够
                    if isinstance(usdt_balance, (int, float)) and usdt_balance < 10:
                        logger.warning(f"USDT余额较低: {usdt_balance}，请确保有足够资金进行交易")
                        
                except Exception as balance_error:
                    logger.warning(f"余额获取失败: {balance_error}")
                    logger.warning("余额获取失败，但这可能是API权限问题，继续运行...")
                
                logger.info("API连接测试完成！")
                return True
                
            except asyncio.TimeoutError:
                logger.error(f"连接测试超时 (尝试 {attempt + 1}/{max_retries})")
            except ccxt_async.AuthenticationError as e:
                logger.error(f"API密钥认证失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                logger.error("请检查API密钥和密钥权限设置")
            except (ccxt_async.NetworkError, ccxt_async.RequestTimeout) as e:
                logger.error(f"网络连接失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if self.config.get('use_proxy'):
                    logger.error("请检查代理服务器是否正常运行")
            except Exception as e:
                logger.error(f"连接测试失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                logger.error(f"详细错误信息: {type(e).__name__}: {str(e)}")
            
            if attempt < max_retries - 1:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                await asyncio.sleep(retry_delay)
        
        logger.error("所有连接测试尝试均失败")
        return False

    # --------------------------------------------------------------------------
    # --- 启动与生命周期管理 ---
    # --------------------------------------------------------------------------
    async def setup(self):
        """执行所有异步的启动设置"""
        logger.info("正在执行启动设置...")
        
        try:
            if not await self.test_connection(): raise Exception("API连接测试失败")
            
            logger.info("正在加载市场数据...")
            await self.exchange.load_markets(True)
            if not self.exchange.markets or self.symbol not in self.exchange.markets:
                raise Exception(f"交易对 {self.symbol} 不存在")
            self.market = self.exchange.market(self.symbol)
            logger.info(f"成功加载市场数据，合约精度: {self.market['precision']}")
            
            # [核心改造] 从交易所市场数据中动态获取最小订单名义价值
            min_cost = self.market.get('limits', {}).get('cost', {}).get('min')
            if min_cost is not None:
                self.min_notional_value = float(min_cost)
                logger.info(f"已从交易所动态获取最小订单名义价值: {self.min_notional_value} USDT")
            else:
                logger.warning(f"无法从交易所获取最小名义价值，将使用默认值: {self.min_notional_value} USDT")
            
            await self.set_hedge_mode()
            await self.set_leverage()
            await self.full_state_sync()
            
            # [新增] 初始化全局风控状态
            try:
                balance = await self.exchange.fetch_balance()
                equity = float(balance.get('info', {}).get('totalWalletBalance', 0))
                if equity <= 0 and 'USDT' in balance:
                    equity = balance['USDT'].get('total', 0)
                
                if equity <= 0:
                    raise ValueError("无法获取有效账户权益")

                self.initial_equity = equity
                self.peak_equity = equity
                logger.info(f"全局风控启动：初始权益 = {self.initial_equity:.2f} USDT, 峰值权益 = {self.peak_equity:.2f} USDT")
            except Exception as e:
                logger.critical(f"无法初始化账户权益，风控模块将无法运行: {e}")
                raise

            if self.last_price == 0:
                bids_asks = await self.exchange.fetch_bids_asks([self.symbol])
                if bids_asks and self.symbol in bids_asks and bids_asks[self.symbol].get('bid'):
                     self.last_price = float(bids_asks[self.symbol]['bid'])
                else:
                    raise Exception("无法在启动时获取初始价格")

            await self.update_price_series()
            self._initialize_grid_pairs() # [新增] 初始化配对网格
            
            self.listen_key = await self.fetch_listen_key()
            asyncio.create_task(self.keep_listen_key_alive())
            asyncio.create_task(self.monitor_account_drawdown()) # [新增] 启动账户回撤监控
            
            logger.info("初始化设置完成。")
            logger.info(f"当前持仓: 多头={self.long_position_size}, 空头={self.short_position_size}")
            
        except Exception as e:
            logger.error(f"初始化设置失败: {e}", exc_info=True)
            raise

    async def run(self):
        """策略主循环，负责WebSocket连接和消息处理"""
        await self.setup()
        await self.cancel_all_open_orders()
        logger.info("所有现有挂单已取消，准备启动策略...")
        
        connection_failures = 0
        max_connection_failures = 10
        
        while not self.is_shutting_down:
            try:
                stream_url = self.websocket_base_url + f"/ws/{self.listen_key}"
                logger.info(f"正在连接WebSocket: {stream_url}")
                
                # 增加WebSocket连接的超时和重试配置
                additional_headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
                
                async with websockets.connect(
                    stream_url, 
                    ping_interval=30,  # 增加ping间隔
                    ping_timeout=10,   # ping超时
                    close_timeout=10,  # 关闭超时
                    max_size=2**20,    # 最大消息大小
                    additional_headers=additional_headers
                ) as websocket:
                    logger.info("WebSocket 连接成功。")
                    connection_failures = 0  # 重置失败计数
                    
                    # 订阅ticker数据流
                    market_id = self.market['id'].lower() if self.market and 'id' in self.market else (self.config['coin_name'].lower() + 'usdt')
                    ticker_payload = {"method": "SUBSCRIBE", "params": [f"{market_id}@bookTicker"], "id": 1}
                    
                    try:
                        await asyncio.wait_for(websocket.send(json.dumps(ticker_payload)), timeout=10)
                        logger.info(f"已发送Ticker订阅请求: {ticker_payload}")
                    except Exception as send_error:
                        logger.error(f"发送订阅请求失败: {send_error}")
                        continue
                    
                    await self.full_state_sync() # 连接成功后立即同步一次
                    
                    # 消息接收循环
                    while not self.is_shutting_down:
                        try:
                            message = await asyncio.wait_for(websocket.recv(), timeout=90)  # 增加超时时间
                            data = json.loads(message)
                            
                            # 处理不同类型的消息
                            if 'e' in data:
                                await self.handle_websocket_message(data)
                            elif 'result' in data:
                                logger.debug(f"订阅确认: {data}")
                            elif 'error' in data:
                                logger.error(f"WebSocket错误: {data}")
                                break
                                
                        except asyncio.TimeoutError:
                            logger.warning("WebSocket接收消息超时，检查连接状态...")
                            # 发送ping检查连接
                            try:
                                await asyncio.wait_for(websocket.ping(), timeout=5)
                                logger.debug("WebSocket ping成功，连接正常")
                                continue
                            except Exception:
                                logger.warning("WebSocket ping失败，连接可能已断开，将重连")
                                break
                                
                        except websockets.exceptions.ConnectionClosed as e:
                            logger.warning(f"WebSocket连接已关闭 ({e.code}): {e.reason}")
                            break
                        except json.JSONDecodeError as e:
                            logger.warning(f"JSON解析失败: {e}")
                            continue
                        except Exception as e:
                            logger.error(f"处理WebSocket消息时出错: {e}")
                            continue
            
            except Exception as e:
                connection_failures += 1
                logger.error(f"WebSocket连接失败 ({connection_failures}/{max_connection_failures}): {e}")
                
                if connection_failures >= max_connection_failures:
                    logger.critical("WebSocket连接失败次数过多，程序退出")
                    break
            
            if self.is_shutting_down: break
            
            # 计算重连延迟（指数退避）
            retry_delay = min(self.config['system']['connection_retry_delay'] * (2 ** min(connection_failures, 5)), 60)
            logger.info(f"等待 {retry_delay} 秒后重连...")
            await asyncio.sleep(retry_delay)
            
            # 重新获取listen key
            try:
                self.listen_key = await self.fetch_listen_key()
                logger.info("已获取新的ListenKey")
            except Exception as lk_e:
                logger.error(f"重连时获取ListenKey失败: {lk_e}")
                connection_failures += 1


    async def close(self):
        """优雅关闭程序，取消所有挂单并关闭连接"""
        if self.is_shutting_down: return
        self.is_shutting_down = True
        logger.info("正在关闭程序...")
        
        # 尝试取消挂单，但不要因为失败而阻止程序关闭
        try:
            if self.exchange and hasattr(self, 'market') and self.market:
                await asyncio.wait_for(self.cancel_all_open_orders(), timeout=10)
        except Exception as e:
            logger.warning(f"关闭时取消挂单失败，但程序将继续关闭: {e}")
        
        # 关闭交易所连接
        try:
            if self.exchange:
                await self.exchange.close()
        except Exception as e:
            logger.warning(f"关闭交易所连接时出错: {e}")
            
        logger.info("程序已关闭。")

    # --------------------------------------------------------------------------
    # --- 核心交易逻辑 ---
    # --------------------------------------------------------------------------
    async def handle_websocket_message(self, data):
        """处理来自WebSocket的消息，分发到不同的处理器"""
        try:
            # 检查数据有效性
            if not isinstance(data, dict):
                logger.debug(f"收到非字典类型消息: {type(data)}")
                return
                
            event_type = data.get("e")
            
            # 处理ticker数据
            if event_type == "bookTicker":
                # 验证必要字段
                if all(key in data for key in ['b', 'a']):
                    current_time_ms = time.time() * 1000
                    if current_time_ms - self.last_ticker_time > self.config['system']['ticker_update_interval_ms']:
                        self.last_ticker_time = current_time_ms
                        await self.handle_ticker_update(data)
                else:
                    logger.warning(f"Ticker数据缺少必要字段: {data}")
                    
            # 处理订单更新
            elif event_type == "ORDER_TRADE_UPDATE":
                if 'o' in data:
                    await self.handle_order_update(data)
                else:
                    logger.warning(f"订单更新消息缺少'o'字段: {data}")
                    
            # 处理ListenKey过期
            elif event_type == "listenKeyExpired":
                logger.warning("ListenKey 已过期，主循环将自动重连。")
                raise websockets.exceptions.ConnectionClosed(None, None)
                
            # 处理账户更新
            elif event_type == "ACCOUNT_UPDATE":
                logger.info(f"收到账户更新: {data}")
                
            # 处理其他消息类型
            elif event_type:
                logger.debug(f"收到未处理的事件类型: {event_type}")
            else:
                # 可能是订阅确认或其他系统消息
                if 'result' in data:
                    logger.debug(f"收到订阅确认: {data}")
                elif 'error' in data:
                    logger.error(f"WebSocket错误消息: {data}")
                else:
                    logger.debug(f"收到未知消息格式: {data}")
                    
        except Exception as e:
            logger.error(f"处理WebSocket消息时发生错误: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.debug(f"问题消息: {data}")

    async def handle_ticker_update(self, data):
        """处理行情更新并触发策略调整"""
        try:
            # 验证数据完整性
            if not all(key in data for key in ['b', 'a']):
                logger.warning(f"Ticker数据缺少必要字段: {data}")
                return
                
            # 解析价格数据
            try:
                bid_price = float(data['b'])
                ask_price = float(data['a'])
                
                # 验证价格合理性
                if bid_price <= 0 or ask_price <= 0 or bid_price > ask_price:
                    logger.warning(f"价格数据异常: bid={bid_price}, ask={ask_price}")
                    return
                    
                self.best_bid_price = bid_price
                self.best_ask_price = ask_price
                self.last_price = (self.best_bid_price + self.best_ask_price) / 2
                
            except (ValueError, TypeError) as e:
                logger.error(f"价格数据转换失败: {e}, 数据: {data}")
                return
            
            # 定期同步状态
            current_time = time.time()
            if current_time - self.last_state_sync_time > self.config['system']['sync_interval_seconds']:
                try:
                    await self.full_state_sync()
                    if self.config['strategy_type'] == 'adaptive_grid':
                        await self.update_price_series()
                except Exception as e:
                    logger.error(f"状态同步失败: {e}")
                    # 继续执行策略，不因同步失败而中断

            # 执行策略
            try:
                await self.execute_strategy()
            except Exception as e:
                logger.error(f"策略执行失败: {e}", exc_info=True)
                
        except Exception as e:
            logger.error(f"处理ticker更新时发生未预期错误: {e}", exc_info=True)
            logger.debug(f"Ticker数据: {data}")

    async def execute_strategy(self):
        """根据策略类型执行策略"""
        try:
            # [新增] 检查策略是否已被永久停止
            if self.is_permanently_stopped:
                logger.warning("策略因触发最大回撤而被永久停止。")
                return

            # 策略执行频率控制 - 防止过度频繁执行
            current_time = time.time()
            # 根据策略类型获取不同的执行间隔
            strategy_type = self.config.get('strategy_type')
            if strategy_type == 'adaptive_grid':
                interval_cfg_path = self.config['strategy_config']['adaptive_grid']
            else:
                interval_cfg_path = {} # 固定网格暂无独立间隔配置
            
            strategy_interval = interval_cfg_path.get('strategy_execution_interval_seconds', 2.0)
            if current_time - self.last_strategy_execution_time < strategy_interval:
                return

            self.last_strategy_execution_time = current_time

            # 检查基础条件（无需锁）
            if not self.last_price or self.last_price <= 0:
                logger.debug("价格数据无效，跳过策略执行")
                return

            # 自适应网格需要K线数据
            if strategy_type == 'adaptive_grid' and (self.price_series is None or len(self.price_series) < 20):
                logger.debug("自适应网格需要价格序列数据，数据不足，跳过策略执行")
                return

            # 检查暂停状态（需要锁保护）
            async with self.lock:
                current_time = time.time()
                if self.is_paused and current_time < self.pause_end_time:
                    logger.debug(f"策略暂停中，剩余 {self.pause_end_time - current_time:.0f} 秒")
                    return
                elif self.is_paused:
                    logger.info("暂停期结束，恢复策略运行。")
                    self.is_paused = False
            
            # --- [新增] 固定网格价格区间检查 ---
            if strategy_type == 'fixed_grid':
                fixed_cfg = self.config['strategy_config']['fixed_grid']
                range_high = fixed_cfg['range_high']
                range_low = fixed_cfg['range_low']
                
                if self.last_price > range_high or self.last_price < range_low:
                    action = fixed_cfg.get('on_breach_action', 'warn')
                    logger.warning(f"价格 {self.last_price} 突破固定区间 [{range_low}, {range_high}]。触发动作: {action}")
                    
                    if action == 'pause':
                        async with self.lock:
                            pause_duration = fixed_cfg.get('pause_duration_on_breach_seconds', 3600)
                            self.is_paused = True
                            self.pause_end_time = time.time() + pause_duration
                            logger.info(f"策略将暂停 {pause_duration} 秒。")
                        await self.cancel_all_open_orders()
                        return # 暂停期间不执行后续逻辑
                        
                    elif action == 'stop':
                        logger.critical("触发紧急止损程序！")
                        await self.trigger_emergency_stop()
                        return # 停止后不执行后续逻辑
            
            # [核心改造] 配对模式下，主要逻辑是定期检查并放置新的买单
            await self.place_new_buy_orders()

        except Exception as e:
            logger.error(f"策略执行过程中发生未预期错误: {e}", exc_info=True)

    def _initialize_grid_pairs(self):
        """
        [新增] 根据策略配置，初始化grid_pairs列表。
        对于固定网格，这只会在启动时执行一次。
        """
        strategy_type = self.config.get('strategy_type')
        cfg = self.config['strategy_config']
        
        if strategy_type == "fixed_grid":
            fixed_cfg = cfg['fixed_grid']
            grid_prices = self.generate_arithmetic_grids(
                fixed_cfg['range_high'], fixed_cfg['range_low'], fixed_cfg['grid_count']
            )
            
            if len(grid_prices) < 2:
                logger.critical("生成的网格点不足以创建配对，请检查配置。程序将退出。")
                sys.exit(1)

            # 创建配对
            for i in range(len(grid_prices) - 1):
                buy_price = grid_prices[i]
                sell_price = grid_prices[i+1]
                self.grid_pairs.append({
                    "buy_price": buy_price,
                    "sell_price": sell_price,
                    "status": "EMPTY",  # EMPTY, BUY_PLACED, SELL_PLACED
                    "buy_order_id": None,
                    "sell_order_id": None,
                    "quantity": 0.0
                })
            logger.info(f"已成功初始化 {len(self.grid_pairs)} 个固定网格配对。")

            # [核心改造] 在此一次性计算并验证订单数量
            total_investment = self.config['strategy_config']['total_investment']
            leverage = self.config['leverage']
            total_notional_value = total_investment * leverage
            
            if not self.grid_pairs:
                logger.critical("未生成任何网格配对，程序退出。")
                sys.exit(1)

            # 验证平均名义价值是否满足交易所要求
            avg_notional_per_grid = total_notional_value / len(self.grid_pairs)
            min_notional_value = self.min_notional_value # [核心改造] 使用动态获取的值
            if avg_notional_per_grid < min_notional_value:
                logger.critical(f"!!! 配置致命错误：根据总网格数计算，每个买单的平均名义价值 "
                              f"({avg_notional_per_grid:.2f} USDT) 低于交易所要求的最小名义价值 "
                              f"({min_notional_value:.2f} USDT)。")
                logger.critical(f"请增加 'total_investment' (当前 {total_investment}) 或减少网格数量 'grid_count' "
                              f"来解决此问题。程序将退出。")
                sys.exit(1)

            # 计算并存储固定的订单数量
            # 使用所有买入价格的总和来计算，这更精确
            total_buy_price_sum = sum(p['buy_price'] for p in self.grid_pairs)
            if total_buy_price_sum > 0:
                self.order_quantity_per_grid = total_notional_value / total_buy_price_sum
                logger.info(f"计划投入保证金: {total_investment} USDT, 杠杆: {leverage}x, 总名义价值: {total_notional_value} USDT")
                logger.info(f"根据 {len(self.grid_pairs)} 个网格配对，已计算出固定订单数量: {self.order_quantity_per_grid:.6f}")
            else:
                logger.critical("所有买入网格价格总和为0，无法计算订单数量。程序退出。")
                sys.exit(1)

        elif strategy_type == "adaptive_grid":
            # TODO: 自适应网格的配对初始化和重绘逻辑需要更复杂的处理
            # 目前暂时只支持固定网格
            logger.warning("配对挂单模式目前主要为 'fixed_grid' 设计。")


    def generate_grid_state(self, price_series, old_grids):
        """单一决策源：根据策略类型计算并决定网格的最终形态"""
        # [说明] 此函数在配对模式下的作用被削弱，初始化在 _initialize_grid_pairs 中完成
        strategy_type = self.config.get('strategy_type')
        cfg = self.config['strategy_config']

        if strategy_type == "fixed_grid":
            if not old_grids:  # 仅在首次生成时执行
                fixed_cfg = cfg['fixed_grid']
                new_grids = self.generate_arithmetic_grids(
                    fixed_cfg['range_high'], fixed_cfg['range_low'], fixed_cfg['grid_count']
                )
                logger.info(f"固定网格已生成: {len(new_grids)}个点位，范围 [{fixed_cfg['range_low']}, {fixed_cfg['range_high']}]")
                return self.GridState(grids=new_grids, center=0, low=0, high=0, should_repaint=True)
            else:
                return self.GridState(grids=old_grids, center=0, low=0, high=0, should_repaint=False)

        elif strategy_type == "adaptive_grid":
            adaptive_cfg = cfg['adaptive_grid']
            try:
                if len(price_series) < adaptive_cfg['ema_anchor_period']:
                    logger.debug("价格数据不足，使用旧网格")
                    return self.GridState(grids=old_grids, center=0, low=0, high=0, should_repaint=False)
                    
                if old_grids and len(old_grids) > 0:
                    current_price = price_series['close'].iloc[-1]
                    if hasattr(self, '_last_grid_price') and self._last_grid_price > 0:
                        price_change_pct = abs(current_price - self._last_grid_price) / self._last_grid_price
                        if price_change_pct < 0.01:
                            logger.debug(f"价格变化较小({price_change_pct:.3%})，保持现有网格")
                            return self.GridState(grids=old_grids, center=0, low=0, high=0, should_repaint=False)
                            
                self._last_grid_price = price_series['close'].iloc[-1]

                anchor = price_series['close'].ewm(span=adaptive_cfg['ema_anchor_period'], adjust=False).mean().iloc[-1]
                std_dev = price_series['close'].rolling(window=adaptive_cfg['ema_anchor_period']).std().iloc[-1]
                grid_low = anchor - (std_dev * adaptive_cfg['bb_std_dev_multiplier'])
                grid_high = anchor + (std_dev * adaptive_cfg['bb_std_dev_multiplier'])

                new_grids = self.generate_linear_grids(center=anchor, low=grid_low, high=grid_high, spacing_pct=adaptive_cfg['grid_spacing_pct'])
                
                overlap_ratio = self.calculate_grid_overlap(new_grids, old_grids)
                should_repaint_flag = overlap_ratio < adaptive_cfg['repaint_overlap_ratio'] if old_grids else True

                return self.GridState(
                    grids=new_grids, center=anchor, low=grid_low, high=grid_high, should_repaint=should_repaint_flag
                )
            except Exception as e:
                logger.error(f"生成自适应网格状态失败: {e}", exc_info=True)
                return self.GridState(grids=old_grids, center=0, low=0, high=0, should_repaint=False)

        else:
            logger.error(f"未知的策略类型: {strategy_type}")
            return self.GridState(grids=old_grids, center=0, low=0, high=0, should_repaint=False)

    def get_trading_direction(self):
        """根据配置决定交易方向"""
        override = self.config['strategy_config']['adaptive_grid']['trend_override'].upper()
        if override in ["LONG_ONLY", "SHORT_ONLY"]: return override
        
        ema_trend = self.price_series['close'].ewm(span=self.config['strategy_config']['adaptive_grid']['ema_trend_period'], adjust=False).mean().iloc[-1]
        return "LONG_ONLY" if self.last_price > ema_trend else "SHORT_ONLY"

    # --------------------------------------------------------------------------
    # --- 订单与状态管理 ---
    # --------------------------------------------------------------------------
    async def place_new_buy_orders(self):
        """
        [新增] 遍历所有网格配对，为处于EMPTY状态的配对挂上新的买单。
        这是配对模式下的主要周期性任务。
        """
        # --- 价格和数据有效性检查 ---
        if self.best_bid_price <= 0 or not self.grid_pairs:
            return

        # --- 计算统一的订单数量 ---
        # [核心改造] 基于总投资额，计算出统一的、固定的订单合约数量
        # total_investment = self.config['strategy_config']['total_investment'] # 移除动态计算
        # leverage = self.config['leverage'] # 移除动态计算
        # total_notional_value = total_investment * leverage # 移除动态计算
        # logger.info(f"计划投入保证金: {total_investment} USDT, 杠杆: {leverage}x, 总名义价值: {total_notional_value} USDT") # 移除动态计算

        # 只为当前价格下方且状态为EMPTY的网格分配资金
        empty_buy_prices = [
            p['buy_price'] for p in self.grid_pairs 
            if p['status'] == 'EMPTY' and p['buy_price'] < self.last_price
        ]
        
        order_quantity = 0.0
        if not empty_buy_prices:
            # logger.debug("当前价格下方无新的买入点位。")
            return
        
        # [核心改造] 不再动态计算，直接使用初始化时计算好的固定值
        order_quantity = self.order_quantity_per_grid
        if order_quantity <= 0:
            logger.error("初始化时计算的订单数量无效，本轮不挂单。")
            return

        # --- 遍历并下单 ---
        place_tasks = []
        pairs_to_update = []
        
        # 在锁外准备任务，减少锁占用时间
        for pair in self.grid_pairs:
            if pair['status'] == 'EMPTY' and pair['buy_price'] < self.last_price:
                pairs_to_update.append(pair)

        if not pairs_to_update:
            return

        logger.info(f"准备为 {len(pairs_to_update)} 个空的网格配对创建买单，统一数量: {order_quantity:.6f}")
        for pair in pairs_to_update:
            # 使用信号量限制并发
            async with self.order_semaphore:
                try:
                    # 在下单前，再次检查状态，避免在 handle_order_update 中状态被改变
                    async with self.lock:
                        if pair['status'] != 'EMPTY':
                            continue # 状态已改变，跳过

                    new_order = await self.place_order(
                        side='buy',
                        position_side='LONG',
                        amount=order_quantity,
                        price=pair['buy_price']
                    )
                    if new_order and 'clientOrderId' in new_order:
                        async with self.lock:
                            pair['status'] = 'BUY_PLACED'
                            pair['buy_order_id'] = new_order['clientOrderId']
                        logger.info(f"为配对 {pair['buy_price']}->{pair['sell_price']} 创建买单成功。")
                    
                    await asyncio.sleep(0.1) # 简易限速

                except Exception as e:
                    logger.error(f"为配对 {pair['buy_price']} 创建买单失败: {e}")


    async def rebalance_grid_orders(self, target_grids):
        """
        [重构] 此函数在配对挂单模式下已废弃。
        其功能被 place_new_buy_orders 和 handle_order_update 替代。
        保留空函数以避免意外调用。
        """
        logger.debug("rebalance_grid_orders 在配对模式下被跳过。")
        return

    async def handle_order_update(self, data):
        """处理订单更新消息，并更新风控状态"""
        try:
            order_data = data.get("o", {})
            if not self.market or order_data.get("s") != self.market['id']: 
                return
            
            # 验证订单数据完整性
            required_fields = ['X', 'c', 'S', 'ps']
            if not all(field in order_data for field in required_fields):
                logger.warning(f"订单数据缺少必要字段: {order_data}")
                return

            client_order_id = order_data['c']
            status = order_data['X']
            
            target_pair = None
            is_buy_order = False

            async with self.lock:
                # --- 在网格配对中寻找订单 ---
                for pair in self.grid_pairs:
                    if pair['buy_order_id'] == client_order_id:
                        target_pair = pair
                        is_buy_order = True
                        break
                    if pair['sell_order_id'] == client_order_id:
                        target_pair = pair
                        is_buy_order = False
                        break
                
                # --- 如果不是配对订单，走旧的逻辑 ---
                if not target_pair:
                    if status in ["CANCELED", "EXPIRED", "REJECTED"]:
                        if client_order_id in self.open_orders: 
                            del self.open_orders[client_order_id]
                    elif status == "FILLED" and client_order_id in self.open_orders:
                        del self.open_orders[client_order_id]
                    # 对于非配对订单，我们只记录日志，不做进一步处理
                    # logger.debug(f"收到一个非配对的订单更新: {client_order_id}, Status: {status}")
                    return

                # --- 处理配对订单 ---
                logger.info(f"配对订单更新: Pair {target_pair['buy_price']}->{target_pair['sell_price']}, "
                          f"Side: {'BUY' if is_buy_order else 'SELL'}, Status: {status}")

                if status == 'FILLED':
                    if is_buy_order:
                        # [配对逻辑] 买单成交，立即挂卖单
                        filled_qty = float(order_data.get('l', 0))
                        filled_price = float(order_data.get('L', 0))
                        
                        if filled_qty <= 0: return

                        logger.info(f"买单成交: {filled_qty} @ {filled_price}。准备挂配对卖单于 {target_pair['sell_price']}")
                        
                        target_pair['status'] = 'SELL_PENDING' # 设置一个临时状态，防止重入
                        target_pair['quantity'] = filled_qty
                        target_pair['buy_order_id'] = None
                        
                        # 在锁外执行下单IO操作
                        asyncio.create_task(self._place_paired_sell_order(target_pair))

                    else: # 卖单成交
                        logger.info(f"卖单成交，配对 {target_pair['buy_price']}->{target_pair['sell_price']} 完成一个周期。")
                        # [配对逻辑] 卖单成交，重置配对为空闲
                        target_pair['status'] = 'EMPTY'
                        target_pair['sell_order_id'] = None
                        
                        # --- [新增] 记录交易历史 ---
                        profit = (target_pair['sell_price'] - target_pair['buy_price']) * target_pair['quantity']
                        self.trade_history.append({
                            "time": pd.Timestamp.now(),
                            "buy_price": target_pair['buy_price'],
                            "sell_price": target_pair['sell_price'],
                            "quantity": target_pair['quantity'],
                            "profit": profit
                        })
                        if len(self.trade_history) > 20: # 保留最近20条
                            self.trade_history.pop(0)
                        self.completed_cycles += 1
                        
                        target_pair['quantity'] = 0.0
                        
                        # 周期性任务 place_new_buy_orders 将会找到这个 EMPTY 的配对并重新挂买单

                elif status in ['CANCELED', 'EXPIRED', 'REJECTED']:
                     # [配对逻辑] 任何终结状态都重置配对为空闲
                    logger.info(f"订单终结，重置配对 {target_pair['buy_price']}->{target_pair['sell_price']} 为空闲状态。")
                    target_pair['status'] = 'EMPTY'
                    target_pair['buy_order_id'] = None
                    target_pair['sell_order_id'] = None
                    target_pair['quantity'] = 0.0

        except Exception as e:
            logger.error(f"处理订单更新时发生未预期错误: {e}", exc_info=True)
            logger.debug(f"订单数据: {data}")

    async def _place_paired_sell_order(self, pair):
        """[新增] 一个独立的任务，用于放置配对的卖单，包含重试逻辑"""
        try:
            new_order = await self.place_order(
                side='sell',
                position_side='LONG',
                amount=pair['quantity'],
                price=pair['sell_price']
            )
            if new_order and 'clientOrderId' in new_order:
                async with self.lock:
                    pair['status'] = 'SELL_PLACED'
                    pair['sell_order_id'] = new_order['clientOrderId']
                logger.info(f"为配对 {pair['buy_price']}->{pair['sell_price']} 创建卖单成功。")
            else:
                raise Exception("下单返回数据无效")
        except Exception as e:
            logger.critical(f"CRITICAL: 为配对 {pair['buy_price']} 创建卖单失败: {e}。状态将保持 PENDING，等待同步修复。")
            # 此时状态为 SELL_PENDING，同步逻辑需要能处理这个状态
    
    async def place_order(self, side, position_side, amount, price):
        """封装统一的下单请求"""
        # 注意：这个函数现在被两个不同的逻辑（初始买单和配对卖单）调用
        try:
            async with self.order_semaphore:
                if not self.market:
                    logger.warning("市场信息未加载，无法下单")
                    return

                # 精度和限制检查
                price = float(self.exchange.price_to_precision(self.symbol, price))
                amount = float(self.exchange.amount_to_precision(self.symbol, amount))
                
                # 生成唯一的客户端订单ID
                client_order_id = f"x-argm-{int(time.time() * 1000)}-{uuid.uuid4().hex[:4]}"
                params = {
                    'positionSide': position_side.upper(),
                    'newClientOrderId': client_order_id,
                    'timeInForce': 'GTC'
                }

                logger.info(f"尝试下单: {side} {position_side} {amount:.4f} @ {price:.4f}")
                new_order = await asyncio.wait_for(
                    self.exchange.create_order(self.symbol, 'limit', side, amount, price, params),
                    timeout=30
                )
                if new_order and 'clientOrderId' in new_order:
                    self.open_orders[new_order['clientOrderId']] = new_order
                    logger.info(f"下单成功: {new_order['clientOrderId']}")
                    return new_order
                else:
                    logger.error(f"下单失败，返回数据异常: {new_order}")
                    return None
        except Exception as e:
            logger.error(f"下单失败: {e}", exc_info=True)
            raise

    async def cancel_order(self, client_order_id):
        """根据客户端订单ID取消订单"""
        try:
            order_to_cancel = self.open_orders.get(client_order_id)
            if not order_to_cancel:
                logger.debug(f"订单不存在于本地记录中: {client_order_id}")
                return

            exchange_order_id = order_to_cancel.get('id')
            if not exchange_order_id:
                logger.warning(f"订单缺少交易所ID: {client_order_id}")
                return

            logger.debug(f"准备撤销订单: ClientID={client_order_id}, ExchangeID={exchange_order_id}")
            await asyncio.wait_for(
                self.exchange.cancel_order(str(exchange_order_id), self.symbol),
                timeout=10
            )
            logger.debug(f"订单撤销成功: ClientID={client_order_id}")
        except Exception as e:
            error_msg = str(e).lower()
            if any(err in error_msg for err in ["order does not exist", "already filled", "unknown order"]):
                logger.debug(f"订单已不存在或已成交: {client_order_id}")
            else:
                logger.error(f"撤单失败: {e}", exc_info=True)
        finally:
            if client_order_id in self.open_orders:
                del self.open_orders[client_order_id]
    
    async def cancel_all_open_orders(self):
        """取消所有挂单"""
        try:
            logger.info(f"正在取消所有 {len(self.open_orders)} 个挂单...")
            await self.exchange.cancel_all_orders(self.symbol)
            self.open_orders.clear()
            logger.info("所有挂单已取消")
        except Exception as e:
            logger.error(f"取消所有挂单失败: {e}", exc_info=True)
            self.open_orders.clear() # 即使失败也清空

    async def _internal_state_sync(self):
        """内部状态同步逻辑，不使用锁（避免死锁）"""
        for attempt in range(3): # [新增] 增加重试逻辑
            try:
                logger.debug(f"开始状态同步 (尝试 {attempt + 1}/3)...")
                
                positions_f = self.exchange.fetch_positions([self.symbol])
                orders_f = self.exchange.fetch_open_orders(self.symbol)
                # [修改] 使用 fetch_bids_asks 获取更可靠的买卖价
                bids_asks_f = self.exchange.fetch_bids_asks([self.symbol])
                
                positions, open_orders, bids_asks = await asyncio.gather(positions_f, orders_f, bids_asks_f, return_exceptions=True)
                
                if isinstance(positions, Exception): raise positions
                if isinstance(open_orders, Exception): raise open_orders
                if isinstance(bids_asks, Exception): raise bids_asks

                # [修改] 增加对bids_asks数据的健壮性检查
                if not bids_asks or self.symbol not in bids_asks:
                    logger.warning(f"获取的Bids/Asks数据结构不完整: {bids_asks}，准备重试...")
                    await asyncio.sleep(1) # 等待1秒后重试
                    continue
                
                ticker = bids_asks[self.symbol]
                if not ticker or ticker.get('bid') is None or ticker.get('ask') is None:
                    logger.warning(f"获取的Ticker数据内容不完整: {ticker}，准备重试...")
                    await asyncio.sleep(1) # 等待1秒后重试
                    continue

                self.best_bid_price = float(ticker['bid'])
                self.best_ask_price = float(ticker['ask'])
                self.last_price = (self.best_bid_price + self.best_ask_price) / 2
                
                long_pos = next((p for p in positions if p.get('info', {}).get('positionSide') == 'LONG'), None)
                short_pos = next((p for p in positions if p.get('info', {}).get('positionSide') == 'SHORT'), None)
                self.long_position_size = float(long_pos.get('contracts', 0)) if long_pos else 0.0
                self.short_position_size = float(short_pos.get('contracts', 0)) if short_pos else 0.0

                valid_orders = {o['clientOrderId']: o for o in open_orders if o.get('clientOrderId')}
                self.open_orders = valid_orders
                
                # [新增] 调用状态核对函数
                await self._reconcile_state(open_orders)

                self.last_state_sync_time = time.time()
                logger.debug(f"状态同步完成。持仓: L={self.long_position_size:.4f}, S={self.short_position_size:.4f}. 挂单: {len(self.open_orders)}")
                return # [新增] 成功后直接返回

            except Exception as e:
                logger.error(f"状态同步失败 (尝试 {attempt + 1}/3): {e}", exc_info=True)
                if attempt < 2:
                    await asyncio.sleep(1) # 等待后重试
                else:
                    logger.critical("所有状态同步尝试均失败，将引发错误。")
                    raise
        raise Exception("状态同步在多次重试后彻底失败")

    async def full_state_sync(self):
        """通过REST API完全同步持仓和挂单状态"""
        async with self.lock:
            await self._internal_state_sync()

    # --------------------------------------------------------------------------
    # --- [新增] 全局硬止损风控 ---
    # --------------------------------------------------------------------------
    async def _reconcile_state(self, open_orders_from_exchange):
        """
        [新增] 状态核对与修复。对比交易所的真实挂单与本地的grid_pairs状态，修复不一致。
        这是保证策略健壮性的关键。
        """
        logger.info("--- 开始状态核对与修复 ---")
        exchange_order_ids = {o['clientOrderId'] for o in open_orders_from_exchange if 'clientOrderId' in o}
        
        for pair in self.grid_pairs:
            status = pair['status']
            buy_id = pair['buy_order_id']
            sell_id = pair['sell_order_id']
            
            # 1. 检查 BUY_PLACED 状态
            if status == 'BUY_PLACED':
                if buy_id not in exchange_order_ids:
                    logger.warning(f"核对异常: 配对 {pair['buy_price']} 状态为 BUY_PLACED，但交易所无此挂单 ({buy_id})。重置为空闲。")
                    pair['status'] = 'EMPTY'
                    pair['buy_order_id'] = None
                    
            # 2. 检查 SELL_PLACED 状态
            elif status == 'SELL_PLACED':
                if sell_id not in exchange_order_ids:
                    logger.warning(f"核对异常: 配对 {pair['buy_price']}->{pair['sell_price']} 状态为 SELL_PLACED，但交易所无此挂单 ({sell_id})。可能已成交或被手动取消。")
                    # 假定它已成交并完成，重置为空闲状态，等待新一轮买入。
                    # 这是一个保守的决定，避免了卡死。最坏的情况是少做了一次，但保证了流动性。
                    pair['status'] = 'EMPTY'
                    pair['sell_order_id'] = None
                    pair['quantity'] = 0.0

            # 3. 检查 SELL_PENDING 状态 (下单失败后的恢复)
            elif status == 'SELL_PENDING':
                logger.warning(f"核对发现: 配对 {pair['buy_price']}->{pair['sell_price']} 处于 SELL_PENDING 状态。将尝试重新为其挂卖单。")
                # 直接重新触发一次卖单放置
                asyncio.create_task(self._place_paired_sell_order(pair))

            # 4. 检查 EMPTY 状态 (不应该有任何关联的挂单)
            elif status == 'EMPTY':
                if buy_id in exchange_order_ids or sell_id in exchange_order_ids:
                    logger.warning(f"核对异常: 配对 {pair['buy_price']} 状态为 EMPTY，但交易所存在其挂单 ({buy_id or sell_id})。将尝试取消。")
                    if buy_id:
                        asyncio.create_task(self.cancel_order(buy_id))
                    if sell_id:
                        asyncio.create_task(self.cancel_order(sell_id))

        logger.info("--- 状态核对与修复完成 ---")


    async def monitor_account_drawdown(self):
        """后台任务，定期监控账户总权益回撤"""
        risk_cfg = self.config['risk_management']
        interval = risk_cfg['drawdown_check_interval_seconds']
        
        while not self.is_shutting_down and not self.is_permanently_stopped:
            try:
                await asyncio.sleep(interval)
                
                if self.is_permanently_stopped: break

                balance = await self.exchange.fetch_balance()
                current_equity = float(balance.get('info', {}).get('totalWalletBalance', 0))
                if current_equity <= 0:
                    continue
                
                # --- [新增] 在更新回撤前，先检查是否可以复投 ---
                if self.reinvestment_cfg.get('enabled'):
                    await self.check_and_reinvest_profit(current_equity)

                self.peak_equity = max(self.peak_equity, current_equity)
                drawdown = (self.peak_equity - current_equity) / self.peak_equity if self.peak_equity > 0 else 0
                
                logger.info(f"权益更新: 当前={current_equity:.2f}, 峰值={self.peak_equity:.2f}, 回撤={drawdown:.2%}")

                # [新增] 记录权益曲线
                self.equity_curve.append({'time': pd.Timestamp.now(), 'equity': current_equity})
                if len(self.equity_curve) > 1000: # 保留最近1000个点
                    self.equity_curve.pop(0)

                if drawdown >= risk_cfg['max_account_drawdown_pct']:
                    logger.critical(f"风控警报：账户回撤达到 {drawdown:.2%}")
                    await self.trigger_emergency_stop()
                    break

            except Exception as e:
                logger.error(f"监控账户回撤时出错: {e}", exc_info=True)

    async def check_and_reinvest_profit(self, current_equity):
        """[新增] 检查并执行利润复投的核心逻辑"""
        async with self.lock:
            if self.initial_equity <= 0 or self.next_reinvest_profit_target <= 0:
                return

            current_profit_pct = (current_equity - self.initial_equity) / self.initial_equity

            if current_profit_pct >= self.next_reinvest_profit_target:
                # --- 触及目标，执行复投 ---
                cfg = self.reinvestment_cfg
                threshold = cfg['profit_threshold_pct']
                ratio = cfg['reinvest_ratio']

                # 计算此阶段对应的利润金额
                profit_amount_for_this_step = self.initial_equity * threshold
                amount_to_reinvest = profit_amount_for_this_step * ratio

                log_header = "=" * 20 + " 🚀 利润复投触发 🚀 " + "=" * 20
                logger.info(log_header)
                logger.info(f"当前利润率 {current_profit_pct:.2%} 已达到目标 {self.next_reinvest_profit_target:.2%}")
                logger.info(f"本阶段利润 ({threshold:.0%}) 对应金额: {profit_amount_for_this_step:.2f} USDT")
                logger.info(f"按复投比例 ({ratio:.0%})，将增加投入: {amount_to_reinvest:.2f} USDT")
                
                # 1. 更新总投资（保证金）
                old_investment = self.config['strategy_config']['total_investment']
                new_investment = old_investment + amount_to_reinvest
                self.config['strategy_config']['total_investment'] = new_investment
                logger.info(f"配置 'total_investment' 已从 {old_investment:.2f} 更新为 {new_investment:.2f}")

                # 2. 重新计算每格订单数量
                leverage = self.config['leverage']
                total_notional_value = new_investment * leverage
                total_buy_price_sum = sum(p['buy_price'] for p in self.grid_pairs)
                if total_buy_price_sum > 0:
                    old_qty = self.order_quantity_per_grid
                    new_qty = total_notional_value / total_buy_price_sum
                    self.order_quantity_per_grid = new_qty
                    logger.info(f"固定订单数量已从 {old_qty:.6f} 更新为 {new_qty:.6f}")
                
                # 3. 更新下一个复投目标
                self.next_reinvest_profit_target += threshold
                logger.info(f"下一个复投目标利润率更新为: {self.next_reinvest_profit_target:.2%}")
                logger.info("=" * (len(log_header) - 1))

    async def trigger_emergency_stop(self):
        """执行紧急止损：清仓、撤单、停机"""
        if self.is_permanently_stopped: return
        self.is_permanently_stopped = True
        logger.info("--- 紧急止损流程开始 ---")
        await self.cancel_all_open_orders()
        await self.close_all_positions()
        logger.critical("所有仓位已平，所有挂单已撤销。策略已永久停止。")

    async def close_all_positions(self):
        """市价平掉所有仓位"""
        try:
            positions = await self.exchange.fetch_positions([self.symbol])
            positions_to_close = [p for p in positions if p.get('contracts') and float(p['contracts']) != 0]
            if not positions_to_close:
                logger.info("当前无持仓，无需执行平仓操作")
                return

            for position in positions_to_close:
                side = 'sell' if position['side'] == 'long' else 'buy'
                amount = float(position['contracts'])
                params = {'reduceOnly': True}
                logger.warning(f"准备提交市价平仓单: {side} {amount} {self.symbol}")
                await self.exchange.create_order(self.symbol, 'market', side, amount, params=params)
        except Exception as e:
            logger.error(f"市价平仓时发生严重错误: {e}", exc_info=True)
            raise

    # --------------------------------------------------------------------------
    # --- 辅助函数 ---
    # --------------------------------------------------------------------------
    async def update_price_series(self):
        """更新用于计算指标的K线数据"""
        try:
            cfg = self.config['strategy_config']['adaptive_grid']
            limit = cfg['ema_trend_period'] + 10
            timeframe = cfg.get('kline_timeframe', '1m')
            
            ohlcv = await asyncio.wait_for(
                self.exchange.fetch_ohlcv(self.symbol, timeframe, limit=limit),
                timeout=30
            )
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            if df.isnull().values.any():
                df = df.dropna()
            self.price_series = df
        except Exception as e:
            logger.error(f"更新K线数据失败: {e}", exc_info=True)

    def generate_arithmetic_grids(self, high, low, count):
        """[新增] 算术（等差）网格生成"""
        if high <= low or count < 2:
            logger.error("算术网格参数无效")
            return []
        
        step = (high - low) / (count - 1)
        grids = [low + i * step for i in range(count)]
        try:
            price_precision = self.market['precision']['price']
            decimals = int(-math.log10(price_precision)) if price_precision > 0 else 4
            rounded_grids = np.round(grids, decimals)
            return sorted(list(set(rounded_grids)))
        except Exception as e:
            logger.error(f"算术网格精度处理失败: {e}")
            return grids

    def generate_linear_grids(self, center, low, high, spacing_pct):
        """线性等比间距网格生成"""
        if high <= low or spacing_pct <= 0: return []
        grids = []
        
        current_price = center
        while (current_price := current_price * (1 + spacing_pct)) <= high:
            grids.append(current_price)
        
        current_price = center
        while (current_price := current_price * (1 - spacing_pct)) >= low:
            grids.append(current_price)
        
        try:
            price_precision = self.market['precision']['price']
            decimals = int(-math.log10(price_precision)) if price_precision > 0 else 4
            rounded_grids = np.round(grids, decimals)
            return sorted(list(set(rounded_grids)))
        except Exception as e:
            logger.error(f"线性网格精度处理失败: {e}")
            return grids
    
    def calculate_grid_overlap(self, new_grids, old_grids):
        """计算新旧网格重合度"""
        if not old_grids or not new_grids: return 0.0
        old_set = set(old_grids)
        new_set = set(new_grids)
        return len(old_set.intersection(new_set)) / len(old_set)

    async def set_hedge_mode(self):
        """设置账户为双向持仓模式"""
        try:
            await self.exchange.fapiPrivatePostPositionSideDual({"dualSidePosition": "true"})
            logger.info("双向持仓模式已成功设置")
        except Exception as e:
            if "No need to change" in str(e):
                logger.info("当前已是双向持仓模式")
            else:
                logger.error(f"设置双向持仓模式失败: {e}", exc_info=True)

    async def set_leverage(self):
        """设置杠杆倍数"""
        try:
            leverage = self.config['leverage']
            await self.exchange.set_leverage(leverage, self.symbol)
            logger.info(f"杠杆已设置为 {leverage}x")
        except Exception as e:
            if "leverage not modified" in str(e):
                logger.info(f"杠杆已经是 {self.config['leverage']}x")
            else:
                logger.error(f"设置杠杆失败: {e}", exc_info=True)

    async def fetch_listen_key(self):
        """获取WebSocket监听密钥"""
        try:
            response = await self.exchange.fapiPrivatePostListenKey({})
            return response['listenKey']
        except Exception as e:
            logger.error(f"获取Listen Key失败: {e}", exc_info=True)
            raise

    async def keep_listen_key_alive(self):
        """后台任务，定期延长 listenKey 的有效期"""
        while not self.is_shutting_down:
            await asyncio.sleep(1800)  # 每30分钟
            try:
                await self.exchange.fapiPrivatePutListenKey({})
                logger.debug("Listen Key有效期已延长")
            except Exception as e:
                logger.error(f"延长Listen Key失败: {e}", exc_info=True)


# ====================================================================================
# --- [新增] 状态报告器 ---
# ====================================================================================
class PeriodicStatusReporter:
    def __init__(self, bot_instance, interval_seconds=60):
        self.bot = bot_instance
        self.interval = interval_seconds

    async def run(self):
        logger.info("状态报告器已启动。")
        while not self.bot.is_shutting_down:
            await asyncio.sleep(self.interval)
            self.display_status_report()
            
    def display_status_report(self):
        bot = self.bot
        if not bot.initial_equity: return # 等待初始化完成

        # --- 核心状态 ---
        report = []
        header = f"====== 策略状态报告 ({pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}) ======"
        report.append(header)
        
        status = "运行中"
        if bot.is_permanently_stopped: status = "已永久停止"
        elif bot.is_paused: status = f"暂停中 (剩余 {bot.pause_end_time - time.time():.0f}s)"
        
        report.append(f"策略状态: {status} | 交易对: {bot.symbol} | 杠杆: {bot.config['leverage']}x")

        # --- 盈亏分析 ---
        current_equity = bot.equity_curve[-1]['equity'] if bot.equity_curve else bot.initial_equity
        pnl = current_equity - bot.initial_equity
        pnl_pct = pnl / bot.initial_equity if bot.initial_equity > 0 else 0
        drawdown = (bot.peak_equity - current_equity) / bot.peak_equity if bot.peak_equity > 0 else 0
        
        report.append("\n--- 整体盈亏分析 ---")
        report.append(f"  初始权益: {bot.initial_equity:.2f} USDT")
        report.append(f"  当前权益: {current_equity:.2f} USDT")
        report.append(f"  浮动盈亏: {pnl:+.2f} USDT ({pnl_pct:+.2%})")
        report.append(f"  峰值权益: {bot.peak_equity:.2f} USDT")
        report.append(f"  当前回撤: {drawdown:.2%}")
        report.append(f"  完成套利次数: {bot.completed_cycles} 次")

        # --- 仓位与风险 ---
        long_value = bot.long_position_size * bot.last_price if bot.last_price > 0 else 0
        report.append("\n--- 仓位与风险 ---")
        report.append(f"  多头仓位: {bot.long_position_size:.4f} ({long_value:.2f} USDT)")
        report.append(f"  当前保证金投入: {bot.config['strategy_config']['total_investment']:.2f} USDT")
        if bot.reinvestment_cfg.get('enabled'):
            report.append(f"  下一复投目标: {bot.next_reinvest_profit_target:.0%}")

        # --- 网格状态分布 ---
        buy_placed = sum(1 for p in bot.grid_pairs if p['status'] == 'BUY_PLACED')
        sell_placed = sum(1 for p in bot.grid_pairs if p['status'] == 'SELL_PLACED')
        empty = sum(1 for p in bot.grid_pairs if p['status'] == 'EMPTY')
        report.append("\n--- 网格状态分布 ---")
        report.append(f"  总配对数: {len(bot.grid_pairs)}")
        report.append(f"  已挂买单: {buy_placed} | 已挂卖单: {sell_placed} | 空闲格数: {empty}")
        
        # --- 近期交易历史 ---
        if bot.trade_history:
            report.append("\n--- 近期完成交易 (最近5条) ---")
            for trade in reversed(bot.trade_history[-5:]):
                report.append(f"  {trade['time'].strftime('%H:%M')} | "
                              f"{trade['quantity']:.4f} @ {trade['buy_price']:.2f} -> {trade['sell_price']:.2f} | "
                              f"利润: {trade['profit']:.4f} USDT")
        
        report.append("=" * len(header))
        
        # 使用换行符连接所有报告行，然后一次性打印
        final_report = "\n".join(report)
        logger.info(f"\n{final_report}\n")


# ====================================================================================
# --- 主程序入口 ---
# ====================================================================================
async def main():
    """主程序入口函数"""
    bot = None
    try:
        logger.info("启动 ARGM-V6.1 策略...")
        bot = ARGMStrategyBot(CONFIG)
        bot.reporter = PeriodicStatusReporter(bot) # [修改] 创建报告器实例
        
        # [修改] 使用 asyncio.gather 来并发运行主逻辑和报告器
        main_task = asyncio.create_task(bot.run())
        reporter_task = asyncio.create_task(bot.reporter.run())
        
        await asyncio.gather(main_task, reporter_task)

    except KeyboardInterrupt:
        logger.info("检测到用户中断 (Ctrl+C)...")
    except Exception as e:
        logger.critical(f"程序顶层发生未捕获的严重错误: {e}", exc_info=True)
    finally:
        if bot:
            await bot.close()
    logger.info("程序已完全退出。")

if __name__ == "__main__":
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())
