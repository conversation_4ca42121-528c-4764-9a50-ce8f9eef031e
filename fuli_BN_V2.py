# ------------------------------------------------------------------------------------
# ARGM-V6.0 "Singularity" - 自适应趋势网格复投策略 (已升级支持固定区间)
# ------------------------------------------------------------------------------------
# 作者: Lens & AI
# 日期: 2025-07-12
# 描述: 本策略是基于第一性原理的自适应网格系统。
#       它将网格的位置、范围、密度及重绘决策统一到一个核心函数中，
#       在捕捉均值回归利润的同时，集成了趋势过滤、智能风险控制和利润复投机制。
# 版本历史:
# V6.1: (本次升级)
#       - 新增 "fixed_grid" 固定区间网格策略模式。
#       - 重构配置结构，引入 strategy_type 开关。
#       - 实现固定区间的算术网格生成与价格突破处理逻辑。
# V6.0: 整合入专业的异步交易框架，替换原有的做市逻辑。
# ------------------------------------------------------------------------------------

import asyncio
import websockets
import json
import logging
import time
import math
import os
import sys
import uuid
import pandas as pd
import numpy as np
from collections import namedtuple
import aiohttp
import ccxt.async_support as ccxt_async


# ====================================================================================
# --- 策略配置 (所有可调参数集中于此) ---
# ====================================================================================
CONFIG = {
    # --- 账户与连接配置 ---
    "api_key": "USlBuHx9zNwxXFtecEQZDOXXxbHhKMIViMeRaELIyjOHGCcdkoESgmxpu4bWi47c",      # ❗❗❗ 替换为你的 API Key
    "api_secret": "lxNiaI54wlWAyxpZ5oNajJ84MoQYodnakDWfoQiuONNTtU9YKsKS14tRwgBi7eUt",  # ❗❗❗ 替换为你的 API Secret
    "use_proxy": True,
    "proxy_url": "http://127.0.0.1:7897",

    # --- 策略核心参数 ---
    "coin_name": "SOL",
    "contract_type": "USDT",
    "leverage": 20,
    "initial_value": 20, # [重构] 含义变为：每格订单的基础名义价值

    # --- [重构] 策略通用配置 ---
    "strategy_type": "fixed_grid", # [新增] 策略类型: "fixed_grid" 或 "adaptive_grid"
    
    "strategy_config": {
        # --- [新增] 固定区间网格配置 (当 strategy_type = "fixed_grid") ---
        "fixed_grid": {
            "range_high": 200.0,   # 宏观区间上限
            "range_low": 180.0,    # 宏观区间下限
            "grid_count": 20,      # 网格数量
            # [新增] 价格突破区间后的行为: "warn", "pause", "stop"
            "on_breach_action": "pause", 
            "pause_duration_on_breach_seconds": 3600, # 暂停时长（秒）
        },

        # --- ARGM-V6.0 自适应网格配置 (当 strategy_type = "adaptive_grid") ---
        "adaptive_grid": {
            "ema_trend_period": 200,      # 宏观趋势判定周期
            "ema_anchor_period": 96,       # 网格中枢EMA周期, 也用作布林带计算周期
            "bb_std_dev_multiplier": 2.0,  # 布林带标准差倍数
            "grid_spacing_pct": 0.005,     # 每个网格的间距百分比 (0.5%)
            "repaint_overlap_ratio": 0.6,  # 新旧网格重合度低于此值则重绘
            "kline_timeframe": '1h',       # 用于计算指标的K线周期
            # 趋势方向手动覆盖开关: "AUTO", "LONG_ONLY", "SHORT_ONLY"
            "trend_override": "LONG_ONLY",
            "strategy_execution_interval_seconds": 10.0,  # 策略执行最小间隔(秒)，防止过度频繁执行
        },
    },

    # --- [新增] 智能风险与复投配置 ---
    "risk_management": {
        # ✅ 多/空头寸合约数量的硬性上限，这是最重要的风控参数
        "position_limit_contracts": 0.5,
        # [新增] 账户最大回撤止损
        "max_account_drawdown_pct": 0.15,  # 15%
        "drawdown_check_interval_seconds": 60,
    },

    # --- 系统运行参数 (基本保持不变) ---
    "system": {
        "sync_interval_seconds": 60,
        "timeout_seconds": 30,
        "ticker_update_interval_ms": 500, # 适当放宽，避免过于频繁调整
        "max_connection_retries": 5,
        "connection_retry_delay": 5,
        "state_sync_on_error": True,
        "websocket_ping_interval": 20,
        "websocket_timeout": 60
    }
}


# ====================================================================================
# --- 日志配置 (保持不变) ---
# ====================================================================================
if not os.path.exists("log"):
    os.makedirs("log")
script_name = os.path.splitext(os.path.basename(__file__))[0]
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - [%(funcName)s] - %(message)s",
    handlers=[
        logging.FileHandler(f"log/{script_name}.log", encoding="utf-8"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger()


# ====================================================================================
# --- 主策略类 ---
# ====================================================================================
class ARGMStrategyBot:
    # [新增] 定义GridState数据结构
    GridState = namedtuple('GridState', ['grids', 'center', 'low', 'high', 'should_repaint'])

    def __init__(self, config):
        """初始化策略实例"""
        self.config = config
        self.lock = asyncio.Lock()
        self.order_semaphore = asyncio.Semaphore(3)  # 限制并发下单数量

        self.exchange = self._initialize_exchange()
        # 修复：使用正确的期货交易对格式
        self.symbol = f"{config['coin_name']}/USDT:USDT"  # 期货交易对格式
        self.market = None
        
        contract_type = self.config['contract_type'].upper()
        if contract_type in ['USDT', 'USDC']:
            self.websocket_base_url = "wss://fstream.binance.com"
        else:
            logger.error(f"不支持的合约类型: {contract_type}"); sys.exit(1)
        logger.info(f"已设置WebSocket URL: {self.websocket_base_url}")

        # --- 实时数据状态 ---
        self.best_bid_price = 0.0
        self.best_ask_price = 0.0
        self.last_price = 0.0
        self.price_series = pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

        # --- 核心策略状态 ---
        self.long_position_size = 0.0
        self.short_position_size = 0.0
        self.net_inventory = 0.0
        self.open_orders = {}
        self.current_grids = [] # [新增] 当前生效的网格

        # [新增] 全局风控状态
        self.initial_equity = 0.0
        self.peak_equity = 0.0
        self.is_permanently_stopped = False
        
        self._last_grid_price = 0.0  # 用于网格重绘频率控制
        
        # [新增] 暂停交易状态
        self.is_paused = False
        self.pause_end_time = 0

        # --- 系统控制变量 ---
        self.listen_key = None
        self.last_state_sync_time = 0
        self.last_ticker_time = 0
        self.last_strategy_execution_time = 0  # 新增：策略执行频率控制
        self.is_shutting_down = False

    def _initialize_exchange(self):
        """初始化并返回CCXT交易所实例"""
        if "YOUR_API_KEY" in self.config['api_key'] or "YOUR_API_SECRET" in self.config['api_secret']:
            logger.error("API密钥未配置，请在CONFIG中设置后再运行。")
            sys.exit(1)

        exchange_class = getattr(ccxt_async, self.config.get('exchange_id', 'binanceusdm'))

        exchange_config = {
            'apiKey': self.config['api_key'],
            'secret': self.config['api_secret'],
            'options': {
                'defaultType': 'future',
                'adjustForTimeDifference': True,
                'recvWindow': 10000,
                'warnOnFetchOpenOrdersWithoutSymbol': False,
                'createMarketBuyOrderRequiresPrice': False
            },
            'timeout': 120000,
            'enableRateLimit': True,
            'rateLimit': 1200,
            'headers': {
                'User-Agent': 'Mozilla/5.0'
            }
        }

        if self.config.get('use_proxy') and self.config.get('proxy_url'):
            exchange_config['aiohttp_proxy'] = self.config['proxy_url']
            logger.info(f"已通过 aiohttp_proxy 设置代理: {self.config['proxy_url']}")

        exchange = exchange_class(exchange_config)
        logger.info("交易所实例创建成功")
        return exchange
    
    async def test_connection(self):
        """测试网络连接和API可用性"""
        max_retries = 5  # 增加重试次数
        retry_delay = 3
        
        for attempt in range(max_retries):
            try:
                logger.info(f"正在测试API连接... (尝试 {attempt + 1}/{max_retries})")
                
                # 1. 首先测试基础网络连接
                logger.info("正在测试基础网络连接...")
                proxy = self.config['proxy_url'] if self.config.get('use_proxy') else None
                
                # 使用更长的超时时间和更好的错误处理
                connector = aiohttp.TCPConnector(
                    limit=100,
                    limit_per_host=30,
                    ttl_dns_cache=300,
                    use_dns_cache=True,
                    keepalive_timeout=30,
                    enable_cleanup_closed=True
                )
                
                timeout = aiohttp.ClientTimeout(
                    total=60,  # 总超时时间
                    connect=30,  # 连接超时
                    sock_read=30  # 读取超时
                )
                
                async with aiohttp.ClientSession(
                    connector=connector,
                    timeout=timeout,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                ) as session:
                    # 测试多个端点以确保连接稳定
                    test_urls = [
                        "https://fapi.binance.com/fapi/v1/ping",
                        "https://fapi.binance.com/fapi/v1/time"
                    ]
                    
                    for test_url in test_urls:
                        try:
                            logger.info(f"测试端点: {test_url}")
                            async with session.get(test_url, proxy=proxy) as response:
                                if response.status == 200:
                                    result = await response.json()
                                    logger.info(f"端点测试成功: {test_url} - {result}")
                                    break
                                else:
                                    logger.warning(f"端点返回状态码: {response.status}")
                        except Exception as url_error:
                            logger.error(f"端点测试失败 {test_url}: {url_error}")
                            if test_url == test_urls[-1]:  # 最后一个URL也失败
                                 raise url_error
                            continue
                
                # 2. 测试CCXT交易所连接
                logger.info("正在测试CCXT交易所连接...")
                try:
                    # 使用更简单的ping方法
                    ping_result = await asyncio.wait_for(
                        self.exchange.publicGetPing(), timeout=45
                    )
                    logger.info(f"CCXT Ping 成功: {ping_result}")
                except Exception as ping_error:
                    logger.warning(f"CCXT Ping失败，尝试备用方法: {ping_error}")
                    # 尝试直接调用时间API
                    try:
                        time_result = await asyncio.wait_for(
                            self.exchange.publicGetTime(), timeout=45
                        )
                        logger.info(f"时间API调用成功: {time_result}")
                    except Exception as time_error:
                        logger.error(f"时间API也失败: {time_error}")
                        raise time_error
                
                # 3. 测试服务器时间获取
                logger.info("正在测试服务器时间获取...")
                try:
                    server_time = await asyncio.wait_for(
                        self.exchange.fetch_time(), timeout=45
                    )
                    logger.info(f"服务器时间获取成功: {server_time}")
                except Exception as time_error:
                    logger.warning(f"服务器时间获取失败: {time_error}")
                    # 继续尝试其他测试

                # 4. 加载市场数据
                logger.info("正在加载市场数据...")
                try:
                    await asyncio.wait_for(self.exchange.load_markets(), timeout=60)
                    logger.info("市场数据加载成功")

                    # 验证交易对是否存在
                    if self.exchange.markets and self.symbol in self.exchange.markets:
                        logger.info(f"交易对 {self.symbol} 验证成功")
                    else:
                        logger.warning(f"交易对 {self.symbol} 不存在，尝试查找替代交易对...")
                        if self.exchange.markets:
                            # 尝试多种交易对格式
                            possible_symbols = [
                                f"{self.config['coin_name']}/USDT:USDT",  # 期货格式
                                f"{self.config['coin_name']}/USDT",       # 现货格式
                                f"{self.config['coin_name']}USDT",        # 简化格式
                            ]
                            
                            found_symbol = None
                            for test_symbol in possible_symbols:
                                if test_symbol in self.exchange.markets:
                                    found_symbol = test_symbol
                                    break
                            
                            if found_symbol:
                                logger.info(f"找到可用交易对: {found_symbol}")
                                self.symbol = found_symbol
                            else:
                                # 显示可用的USDT交易对
                                available_symbols = [s for s in list(self.exchange.markets.keys())[:10] if 'USDT' in s]
                                logger.warning("可用的USDT交易对示例:")
                                for sym in available_symbols:
                                     logger.warning(f"  - {sym}")
                                logger.error(f"无法找到 {self.config['coin_name']} 相关的交易对")
                        else:
                            logger.error("市场数据为空")

                except Exception as market_error:
                    logger.warning(f"市场数据加载失败: {market_error}")
                    logger.warning("市场数据加载失败，但继续测试...")

                # 5. 测试ticker数据获取
                logger.info("正在测试ticker数据获取...")
                try:
                    ticker = await asyncio.wait_for(
                        self.exchange.fetch_ticker(self.symbol), timeout=45
                    )
                    logger.info(f"Ticker 获取成功: {ticker['symbol']} @ {ticker['last']}")
                except Exception as ticker_error:
                    logger.warning(f"Ticker获取失败: {ticker_error}")
                    logger.warning("Ticker获取失败，但这可能是交易对问题，继续测试...")
                
                # 6. 测试账户余额获取 (需要API密钥)
                logger.info("正在测试账户余额获取...")
                try:
                    balance = await asyncio.wait_for(
                        self.exchange.fetch_balance(), timeout=45
                    )
                    usdt_balance = balance.get('USDT', {}).get('total', 'N/A')
                    logger.info(f"余额获取成功，USDT余额: {usdt_balance}")
                    
                    # 检查余额是否足够
                    if isinstance(usdt_balance, (int, float)) and usdt_balance < 10:
                        logger.warning(f"USDT余额较低: {usdt_balance}，请确保有足够资金进行交易")
                        
                except Exception as balance_error:
                    logger.warning(f"余额获取失败: {balance_error}")
                    logger.warning("余额获取失败，但这可能是API权限问题，继续运行...")
                
                logger.info("API连接测试完成！")
                return True
                
            except asyncio.TimeoutError:
                logger.error(f"连接测试超时 (尝试 {attempt + 1}/{max_retries})")
            except ccxt_async.AuthenticationError as e:
                logger.error(f"API密钥认证失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                logger.error("请检查API密钥和密钥权限设置")
            except (ccxt_async.NetworkError, ccxt_async.RequestTimeout) as e:
                logger.error(f"网络连接失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if self.config.get('use_proxy'):
                    logger.error("请检查代理服务器是否正常运行")
            except Exception as e:
                logger.error(f"连接测试失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                logger.error(f"详细错误信息: {type(e).__name__}: {str(e)}")
            
            if attempt < max_retries - 1:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                await asyncio.sleep(retry_delay)
        
        logger.error("所有连接测试尝试均失败")
        return False

    # --------------------------------------------------------------------------
    # --- 启动与生命周期管理 ---
    # --------------------------------------------------------------------------
    async def setup(self):
        """执行所有异步的启动设置"""
        logger.info("正在执行启动设置...")
        
        try:
            if not await self.test_connection(): raise Exception("API连接测试失败")
            
            logger.info("正在加载市场数据...")
            await self.exchange.load_markets(True)
            if not self.exchange.markets or self.symbol not in self.exchange.markets:
                raise Exception(f"交易对 {self.symbol} 不存在")
            self.market = self.exchange.market(self.symbol)
            logger.info(f"成功加载市场数据，合约精度: {self.market['precision']}")
            
            await self.set_hedge_mode()
            await self.set_leverage()
            await self.full_state_sync()
            
            # [新增] 初始化全局风控状态
            try:
                balance = await self.exchange.fetch_balance()
                equity = float(balance.get('info', {}).get('totalWalletBalance', 0))
                if equity <= 0 and 'USDT' in balance:
                    equity = balance['USDT'].get('total', 0)
                
                if equity <= 0:
                    raise ValueError("无法获取有效账户权益")

                self.initial_equity = equity
                self.peak_equity = equity
                logger.info(f"全局风控启动：初始权益 = {self.initial_equity:.2f} USDT, 峰值权益 = {self.peak_equity:.2f} USDT")
            except Exception as e:
                logger.critical(f"无法初始化账户权益，风控模块将无法运行: {e}")
                raise

            if self.last_price == 0: self.last_price = (await self.exchange.fetch_ticker(self.symbol))['last']
            
            await self.update_price_series()
            
            self.listen_key = await self.fetch_listen_key()
            asyncio.create_task(self.keep_listen_key_alive())
            asyncio.create_task(self.monitor_account_drawdown()) # [新增] 启动账户回撤监控
            
            logger.info("初始化设置完成。")
            logger.info(f"当前持仓: 多头={self.long_position_size}, 空头={self.short_position_size}")
            
        except Exception as e:
            logger.error(f"初始化设置失败: {e}", exc_info=True)
            raise

    async def run(self):
        """策略主循环，负责WebSocket连接和消息处理"""
        await self.setup()
        await self.cancel_all_open_orders()
        logger.info("所有现有挂单已取消，准备启动策略...")
        
        connection_failures = 0
        max_connection_failures = 10
        
        while not self.is_shutting_down:
            try:
                stream_url = self.websocket_base_url + f"/ws/{self.listen_key}"
                logger.info(f"正在连接WebSocket: {stream_url}")
                
                # 增加WebSocket连接的超时和重试配置
                additional_headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
                
                async with websockets.connect(
                    stream_url, 
                    ping_interval=30,  # 增加ping间隔
                    ping_timeout=10,   # ping超时
                    close_timeout=10,  # 关闭超时
                    max_size=2**20,    # 最大消息大小
                    additional_headers=additional_headers
                ) as websocket:
                    logger.info("WebSocket 连接成功。")
                    connection_failures = 0  # 重置失败计数
                    
                    # 订阅ticker数据流
                    market_id = self.market['id'].lower() if self.market and 'id' in self.market else (self.config['coin_name'].lower() + 'usdt')
                    ticker_payload = {"method": "SUBSCRIBE", "params": [f"{market_id}@bookTicker"], "id": 1}
                    
                    try:
                        await asyncio.wait_for(websocket.send(json.dumps(ticker_payload)), timeout=10)
                        logger.info(f"已发送Ticker订阅请求: {ticker_payload}")
                    except Exception as send_error:
                        logger.error(f"发送订阅请求失败: {send_error}")
                        continue
                    
                    await self.full_state_sync() # 连接成功后立即同步一次
                    
                    # 消息接收循环
                    while not self.is_shutting_down:
                        try:
                            message = await asyncio.wait_for(websocket.recv(), timeout=90)  # 增加超时时间
                            data = json.loads(message)
                            
                            # 处理不同类型的消息
                            if 'e' in data:
                                await self.handle_websocket_message(data)
                            elif 'result' in data:
                                logger.debug(f"订阅确认: {data}")
                            elif 'error' in data:
                                logger.error(f"WebSocket错误: {data}")
                                break
                                
                        except asyncio.TimeoutError:
                            logger.warning("WebSocket接收消息超时，检查连接状态...")
                            # 发送ping检查连接
                            try:
                                await asyncio.wait_for(websocket.ping(), timeout=5)
                                logger.debug("WebSocket ping成功，连接正常")
                                continue
                            except Exception:
                                logger.warning("WebSocket ping失败，连接可能已断开，将重连")
                                break
                                
                        except websockets.exceptions.ConnectionClosed as e:
                            logger.warning(f"WebSocket连接已关闭 ({e.code}): {e.reason}")
                            break
                        except json.JSONDecodeError as e:
                            logger.warning(f"JSON解析失败: {e}")
                            continue
                        except Exception as e:
                            logger.error(f"处理WebSocket消息时出错: {e}")
                            continue
            
            except Exception as e:
                connection_failures += 1
                logger.error(f"WebSocket连接失败 ({connection_failures}/{max_connection_failures}): {e}")
                
                if connection_failures >= max_connection_failures:
                    logger.critical("WebSocket连接失败次数过多，程序退出")
                    break
            
            if self.is_shutting_down: break
            
            # 计算重连延迟（指数退避）
            retry_delay = min(self.config['system']['connection_retry_delay'] * (2 ** min(connection_failures, 5)), 60)
            logger.info(f"等待 {retry_delay} 秒后重连...")
            await asyncio.sleep(retry_delay)
            
            # 重新获取listen key
            try:
                self.listen_key = await self.fetch_listen_key()
                logger.info("已获取新的ListenKey")
            except Exception as lk_e:
                logger.error(f"重连时获取ListenKey失败: {lk_e}")
                connection_failures += 1


    async def close(self):
        """优雅关闭程序，取消所有挂单并关闭连接"""
        if self.is_shutting_down: return
        self.is_shutting_down = True
        logger.info("正在关闭程序...")
        
        # 尝试取消挂单，但不要因为失败而阻止程序关闭
        try:
            if self.exchange and hasattr(self, 'market') and self.market:
                await asyncio.wait_for(self.cancel_all_open_orders(), timeout=10)
        except Exception as e:
            logger.warning(f"关闭时取消挂单失败，但程序将继续关闭: {e}")
        
        # 关闭交易所连接
        try:
            if self.exchange:
                await self.exchange.close()
        except Exception as e:
            logger.warning(f"关闭交易所连接时出错: {e}")
            
        logger.info("程序已关闭。")

    # --------------------------------------------------------------------------
    # --- 核心交易逻辑 ---
    # --------------------------------------------------------------------------
    async def handle_websocket_message(self, data):
        """处理来自WebSocket的消息，分发到不同的处理器"""
        try:
            # 检查数据有效性
            if not isinstance(data, dict):
                logger.debug(f"收到非字典类型消息: {type(data)}")
                return
                
            event_type = data.get("e")
            
            # 处理ticker数据
            if event_type == "bookTicker":
                # 验证必要字段
                if all(key in data for key in ['b', 'a']):
                    current_time_ms = time.time() * 1000
                    if current_time_ms - self.last_ticker_time > self.config['system']['ticker_update_interval_ms']:
                        self.last_ticker_time = current_time_ms
                        await self.handle_ticker_update(data)
                else:
                    logger.warning(f"Ticker数据缺少必要字段: {data}")
                    
            # 处理订单更新
            elif event_type == "ORDER_TRADE_UPDATE":
                if 'o' in data:
                    await self.handle_order_update(data)
                else:
                    logger.warning(f"订单更新消息缺少'o'字段: {data}")
                    
            # 处理ListenKey过期
            elif event_type == "listenKeyExpired":
                logger.warning("ListenKey 已过期，主循环将自动重连。")
                raise websockets.exceptions.ConnectionClosed(None, None)
                
            # 处理账户更新
            elif event_type == "ACCOUNT_UPDATE":
                logger.info(f"收到账户更新: {data}")
                
            # 处理其他消息类型
            elif event_type:
                logger.debug(f"收到未处理的事件类型: {event_type}")
            else:
                # 可能是订阅确认或其他系统消息
                if 'result' in data:
                    logger.debug(f"收到订阅确认: {data}")
                elif 'error' in data:
                    logger.error(f"WebSocket错误消息: {data}")
                else:
                    logger.debug(f"收到未知消息格式: {data}")
                    
        except Exception as e:
            logger.error(f"处理WebSocket消息时发生错误: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.debug(f"问题消息: {data}")

    async def handle_ticker_update(self, data):
        """处理行情更新并触发策略调整"""
        try:
            # 验证数据完整性
            if not all(key in data for key in ['b', 'a']):
                logger.warning(f"Ticker数据缺少必要字段: {data}")
                return
                
            # 解析价格数据
            try:
                bid_price = float(data['b'])
                ask_price = float(data['a'])
                
                # 验证价格合理性
                if bid_price <= 0 or ask_price <= 0 or bid_price > ask_price:
                    logger.warning(f"价格数据异常: bid={bid_price}, ask={ask_price}")
                    return
                    
                self.best_bid_price = bid_price
                self.best_ask_price = ask_price
                self.last_price = (self.best_bid_price + self.best_ask_price) / 2
                
            except (ValueError, TypeError) as e:
                logger.error(f"价格数据转换失败: {e}, 数据: {data}")
                return
            
            # 定期同步状态
            current_time = time.time()
            if current_time - self.last_state_sync_time > self.config['system']['sync_interval_seconds']:
                try:
                    await self.full_state_sync()
                    if self.config['strategy_type'] == 'adaptive_grid':
                        await self.update_price_series()
                except Exception as e:
                    logger.error(f"状态同步失败: {e}")
                    # 继续执行策略，不因同步失败而中断

            # 执行策略
            try:
                await self.execute_strategy()
            except Exception as e:
                logger.error(f"策略执行失败: {e}", exc_info=True)
                
        except Exception as e:
            logger.error(f"处理ticker更新时发生未预期错误: {e}", exc_info=True)
            logger.debug(f"Ticker数据: {data}")

    async def execute_strategy(self):
        """根据策略类型执行策略"""
        try:
            # [新增] 检查策略是否已被永久停止
            if self.is_permanently_stopped:
                logger.warning("策略因触发最大回撤而被永久停止。")
                return

            # 策略执行频率控制 - 防止过度频繁执行
            current_time = time.time()
            # 根据策略类型获取不同的执行间隔
            strategy_type = self.config.get('strategy_type')
            if strategy_type == 'adaptive_grid':
                interval_cfg_path = self.config['strategy_config']['adaptive_grid']
            else:
                interval_cfg_path = {} # 固定网格暂无独立间隔配置
            
            strategy_interval = interval_cfg_path.get('strategy_execution_interval_seconds', 2.0)
            if current_time - self.last_strategy_execution_time < strategy_interval:
                return

            self.last_strategy_execution_time = current_time

            # 检查基础条件（无需锁）
            if not self.last_price or self.last_price <= 0:
                logger.debug("价格数据无效，跳过策略执行")
                return

            # 自适应网格需要K线数据
            if strategy_type == 'adaptive_grid' and (self.price_series is None or len(self.price_series) < 20):
                logger.debug("自适应网格需要价格序列数据，数据不足，跳过策略执行")
                return

            # 检查暂停状态（需要锁保护）
            async with self.lock:
                current_time = time.time()
                if self.is_paused and current_time < self.pause_end_time:
                    logger.debug(f"策略暂停中，剩余 {self.pause_end_time - current_time:.0f} 秒")
                    return
                elif self.is_paused:
                    logger.info("暂停期结束，恢复策略运行。")
                    self.is_paused = False
            
            # --- [新增] 固定网格价格区间检查 ---
            if strategy_type == 'fixed_grid':
                fixed_cfg = self.config['strategy_config']['fixed_grid']
                range_high = fixed_cfg['range_high']
                range_low = fixed_cfg['range_low']
                
                if self.last_price > range_high or self.last_price < range_low:
                    action = fixed_cfg.get('on_breach_action', 'warn')
                    logger.warning(f"价格 {self.last_price} 突破固定区间 [{range_low}, {range_high}]。触发动作: {action}")
                    
                    if action == 'pause':
                        async with self.lock:
                            pause_duration = fixed_cfg.get('pause_duration_on_breach_seconds', 3600)
                            self.is_paused = True
                            self.pause_end_time = time.time() + pause_duration
                            logger.info(f"策略将暂停 {pause_duration} 秒。")
                        await self.cancel_all_open_orders()
                        return # 暂停期间不执行后续逻辑
                        
                    elif action == 'stop':
                        logger.critical("触发紧急止损程序！")
                        await self.trigger_emergency_stop()
                        return # 停止后不执行后续逻辑
                    
            # 生成网格状态（无需锁，只读操作）
            grid_state = self.generate_grid_state(self.price_series, self.current_grids)
            if not grid_state or not grid_state.grids:
                logger.warning("网格状态生成失败，跳过本次执行")
                return

            # 执行网格调整（在锁外执行，避免长时间占用锁）
            if grid_state.should_repaint:
                log_msg = f"网格重绘触发！"
                if grid_state.center > 0:
                    log_msg += f" 新中枢: {grid_state.center:.4f}, 新范围: [{grid_state.low:.4f}, {grid_state.high:.4f}]"
                logger.info(log_msg)
                
                async with self.lock:
                    self.current_grids = grid_state.grids
                await self.rebalance_grid_orders(grid_state.grids)
            else:
                 # 即使不重绘，也应定期检查并补全订单
                 await self.rebalance_grid_orders(self.current_grids)
                 logger.debug("网格无需重绘，仅检查并同步挂单")

        except Exception as e:
            logger.error(f"策略执行过程中发生未预期错误: {e}", exc_info=True)

    def generate_grid_state(self, price_series, old_grids):
        """单一决策源：根据策略类型计算并决定网格的最终形态"""
        strategy_type = self.config.get('strategy_type')
        cfg = self.config['strategy_config']

        if strategy_type == "fixed_grid":
            if not old_grids:  # 仅在首次生成时执行
                fixed_cfg = cfg['fixed_grid']
                new_grids = self.generate_arithmetic_grids(
                    fixed_cfg['range_high'], fixed_cfg['range_low'], fixed_cfg['grid_count']
                )
                logger.info(f"固定网格已生成: {len(new_grids)}个点位，范围 [{fixed_cfg['range_low']}, {fixed_cfg['range_high']}]")
                return self.GridState(grids=new_grids, center=0, low=0, high=0, should_repaint=True)
            else:
                return self.GridState(grids=old_grids, center=0, low=0, high=0, should_repaint=False)

        elif strategy_type == "adaptive_grid":
            adaptive_cfg = cfg['adaptive_grid']
            try:
                if len(price_series) < adaptive_cfg['ema_anchor_period']:
                    logger.debug("价格数据不足，使用旧网格")
                    return self.GridState(grids=old_grids, center=0, low=0, high=0, should_repaint=False)
                    
                if old_grids and len(old_grids) > 0:
                    current_price = price_series['close'].iloc[-1]
                    if hasattr(self, '_last_grid_price') and self._last_grid_price > 0:
                        price_change_pct = abs(current_price - self._last_grid_price) / self._last_grid_price
                        if price_change_pct < 0.01:
                            logger.debug(f"价格变化较小({price_change_pct:.3%})，保持现有网格")
                            return self.GridState(grids=old_grids, center=0, low=0, high=0, should_repaint=False)
                            
                self._last_grid_price = price_series['close'].iloc[-1]

                anchor = price_series['close'].ewm(span=adaptive_cfg['ema_anchor_period'], adjust=False).mean().iloc[-1]
                std_dev = price_series['close'].rolling(window=adaptive_cfg['ema_anchor_period']).std().iloc[-1]
                grid_low = anchor - (std_dev * adaptive_cfg['bb_std_dev_multiplier'])
                grid_high = anchor + (std_dev * adaptive_cfg['bb_std_dev_multiplier'])

                new_grids = self.generate_linear_grids(center=anchor, low=grid_low, high=grid_high, spacing_pct=adaptive_cfg['grid_spacing_pct'])
                
                overlap_ratio = self.calculate_grid_overlap(new_grids, old_grids)
                should_repaint_flag = overlap_ratio < adaptive_cfg['repaint_overlap_ratio'] if old_grids else True

                return self.GridState(
                    grids=new_grids, center=anchor, low=grid_low, high=grid_high, should_repaint=should_repaint_flag
                )
            except Exception as e:
                logger.error(f"生成自适应网格状态失败: {e}", exc_info=True)
                return self.GridState(grids=old_grids, center=0, low=0, high=0, should_repaint=False)

        else:
            logger.error(f"未知的策略类型: {strategy_type}")
            return self.GridState(grids=old_grids, center=0, low=0, high=0, should_repaint=False)

    def get_trading_direction(self):
        """根据配置决定交易方向"""
        override = self.config['strategy_config']['adaptive_grid']['trend_override'].upper()
        if override in ["LONG_ONLY", "SHORT_ONLY"]: return override
        
        ema_trend = self.price_series['close'].ewm(span=self.config['strategy_config']['adaptive_grid']['ema_trend_period'], adjust=False).mean().iloc[-1]
        return "LONG_ONLY" if self.last_price > ema_trend else "SHORT_ONLY"

    def get_next_order_size(self):
        """统一决定下一次交易的仓位大小"""
        return self.config['initial_value']

    # --------------------------------------------------------------------------
    # --- 订单与状态管理 ---
    # --------------------------------------------------------------------------
    async def rebalance_grid_orders(self, target_grids):
        """对比目标网格与现有订单，执行新增、删除操作（已优化防死锁）"""
        logger.info("=== rebalance_grid_orders函数开始执行 ===")

        # --- 锁外执行I/O密集型操作 ---
        logger.info("开始状态同步 (锁外)...")
        try:
            await asyncio.wait_for(self._internal_state_sync(), timeout=20)
            logger.info("状态同步完成 (锁外)")
        except asyncio.TimeoutError:
            logger.warning("状态同步超时，本次网格调整取消。")
            return
        except Exception as e:
            logger.error(f"状态同步失败，本次网格调整取消: {e}", exc_info=True)
            return

        # --- 锁内执行CPU密集型和内存操作 ---
        logger.debug("准备获取锁进行内存操作...")

        # 先在锁外进行基础验证，减少锁内时间
        if self.best_bid_price <= 0 or self.best_ask_price <= 0:
            logger.warning(f"价格数据无效 (bid={self.best_bid_price}, ask={self.best_ask_price})，跳过订单调整。")
            return

        if not target_grids or not self.last_price or float(self.last_price) <= 0:
            logger.debug("网格参数无效，跳过订单调整")
            return

        # 在锁内快速获取必要信息，然后释放锁
        orders_to_place = []
        orders_to_cancel = []
        order_quantity = 0

        async with self.lock:
            logger.debug("已获取锁，开始执行内存操作...")
            try:
                logger.debug(f"当前挂单: {list(self.open_orders.keys())}")
                logger.debug(f"目标网格价格: {target_grids}")

                existing_prices = {float(o.get('price', 0)) for o in self.open_orders.values() if o.get('price') is not None}
                target_prices = {float(price) for price in target_grids if price is not None and float(price) > 0}

                # 识别需要取消的订单
                orders_to_cancel = [
                    o for o in self.open_orders.values()
                    if o.get('price') is not None and float(o['price']) not in target_prices
                ]
                
                # 获取订单大小
                order_size_usd = self.get_next_order_size()
                if order_size_usd <= 0:
                    logger.warning("订单大小无效，跳过订单调整")
                    return
                order_quantity = order_size_usd / self.last_price
                if order_quantity <= 0:
                    logger.warning(f"计算的订单数量无效: {order_quantity}")
                    return

                # 识别需要下的目标订单
                for price in target_prices:
                    if price not in existing_prices:
                        price_float = float(price)
                        side = 'buy' if price_float < self.last_price else 'sell'
                        
                        # 仅当价格低于当前价时挂买单，高于当前价时挂卖单
                        if side == 'buy':
                            orders_to_place.append({'side': 'buy', 'price': price, 'positionSide': 'LONG'})
                        elif side == 'sell':
                            orders_to_place.append({'side': 'sell', 'price': price, 'positionSide': 'LONG'})

            except Exception as e:
                logger.error(f"网格订单分析过程中发生未预期错误: {e}", exc_info=True)
                return

        # --- 在锁外执行I/O操作，避免死锁 ---
        logger.info(f"开始执行订单操作: 取消{len(orders_to_cancel)}个, 创建{len(orders_to_place)}个")

        if orders_to_cancel:
            cancel_tasks = [self.cancel_order(o.get('clientOrderId')) for o in orders_to_cancel if o.get('clientOrderId')]
            await asyncio.gather(*cancel_tasks, return_exceptions=True)
            logger.info(f"已提交 {len(cancel_tasks)} 个订单取消请求")
            await asyncio.sleep(0.5) # 等待状态更新

        if orders_to_place:
            for order_info in orders_to_place:
                try:
                    await self.place_order(
                        order_info['side'],
                        order_info['positionSide'],
                        order_quantity,
                        order_info['price']
                    )
                    await asyncio.sleep(0.1) # 避免API限速
                except Exception as e:
                    logger.warning(f"创建订单失败 (价格: {order_info['price']}): {e}")
                    if "insufficient" in str(e).lower():
                        logger.warning("余额不足，停止后续下单")
                        break

        try:
            await asyncio.wait_for(self._internal_state_sync(), timeout=10)
            logger.debug("网格调整后状态同步完成")
        except Exception as e:
            logger.error(f"最终状态同步失败: {e}")

        logger.info("=== rebalance_grid_orders函数正常完成 ===")

    async def handle_order_update(self, data):
        """处理订单更新消息，并更新风控状态"""
        try:
            order_data = data.get("o", {})
            if not self.market or order_data.get("s") != self.market['id']: 
                return
            
            # 验证订单数据完整性
            required_fields = ['X', 'c', 'S', 'ps']
            if not all(field in order_data for field in required_fields):
                logger.warning(f"订单数据缺少必要字段: {order_data}")
                return

            async with self.lock:
                try:
                    status = order_data['X']
                    client_order_id = order_data['c']

                    if status in ["CANCELED", "EXPIRED", "REJECTED"]:
                        if client_order_id in self.open_orders: 
                            del self.open_orders[client_order_id]
                        logger.info(f"订单终结 ({status}): ID: {client_order_id}")

                    elif status in ["FILLED", "PARTIALLY_FILLED"]:
                        try:
                            side = order_data['S']
                            position_side = order_data['ps']
                            filled_qty = float(order_data.get('l', 0))
                            filled_price = float(order_data.get('L', 0))
                            
                            if filled_qty <= 0 or filled_price <= 0:
                                logger.warning(f"无效的成交数据: qty={filled_qty}, price={filled_price}")
                                return
                            
                            logger.info(f"订单成交: {side} {position_side} {filled_qty:.4f} @ {filled_price:.4f}")

                            if status == "FILLED" and client_order_id in self.open_orders:
                                del self.open_orders[client_order_id]
                            
                            # 立即同步状态
                            asyncio.create_task(self.full_state_sync())
                                
                        except (ValueError, TypeError, KeyError) as e:
                            logger.error(f"处理成交数据失败: {e}, 订单数据: {order_data}")
                            
                except Exception as e:
                    logger.error(f"处理订单更新时发生错误: {e}", exc_info=True)
                    
        except Exception as e:
            logger.error(f"处理订单更新时发生未预期错误: {e}", exc_info=True)
            logger.debug(f"订单数据: {data}")

    async def place_order(self, side, position_side, amount, price):
        """封装统一的下单请求"""
        try:
            async with self.order_semaphore:
                if not self.market:
                    logger.warning("市场信息未加载，无法下单")
                    return

                # 精度和限制检查
                price = float(self.exchange.price_to_precision(self.symbol, price))
                amount = float(self.exchange.amount_to_precision(self.symbol, amount))
                
                # 生成唯一的客户端订单ID
                client_order_id = f"x-argm-{int(time.time() * 1000)}-{uuid.uuid4().hex[:4]}"
                params = {
                    'positionSide': position_side.upper(),
                    'newClientOrderId': client_order_id,
                    'timeInForce': 'GTC'
                }

                logger.info(f"尝试下单: {side} {position_side} {amount:.4f} @ {price:.4f}")
                new_order = await asyncio.wait_for(
                    self.exchange.create_order(self.symbol, 'limit', side, amount, price, params),
                    timeout=30
                )
                if new_order and 'clientOrderId' in new_order:
                    self.open_orders[new_order['clientOrderId']] = new_order
                    logger.info(f"下单成功: {new_order['clientOrderId']}")
                    return new_order
                else:
                    logger.error(f"下单失败，返回数据异常: {new_order}")
                    return None
        except Exception as e:
            logger.error(f"下单失败: {e}", exc_info=True)
            raise

    async def cancel_order(self, client_order_id):
        """根据客户端订单ID取消订单"""
        try:
            order_to_cancel = self.open_orders.get(client_order_id)
            if not order_to_cancel:
                logger.debug(f"订单不存在于本地记录中: {client_order_id}")
                return

            exchange_order_id = order_to_cancel.get('id')
            if not exchange_order_id:
                logger.warning(f"订单缺少交易所ID: {client_order_id}")
                return

            logger.debug(f"准备撤销订单: ClientID={client_order_id}, ExchangeID={exchange_order_id}")
            await asyncio.wait_for(
                self.exchange.cancel_order(str(exchange_order_id), self.symbol),
                timeout=10
            )
            logger.debug(f"订单撤销成功: ClientID={client_order_id}")
        except Exception as e:
            error_msg = str(e).lower()
            if any(err in error_msg for err in ["order does not exist", "already filled", "unknown order"]):
                logger.debug(f"订单已不存在或已成交: {client_order_id}")
            else:
                logger.error(f"撤单失败: {e}", exc_info=True)
        finally:
            if client_order_id in self.open_orders:
                del self.open_orders[client_order_id]
    
    async def cancel_all_open_orders(self):
        """取消所有挂单"""
        try:
            logger.info(f"正在取消所有 {len(self.open_orders)} 个挂单...")
            await self.exchange.cancel_all_orders(self.symbol)
            self.open_orders.clear()
            logger.info("所有挂单已取消")
        except Exception as e:
            logger.error(f"取消所有挂单失败: {e}", exc_info=True)
            self.open_orders.clear() # 即使失败也清空

    async def _internal_state_sync(self):
        """内部状态同步逻辑，不使用锁（避免死锁）"""
        for attempt in range(3): # [新增] 增加重试逻辑
            try:
                logger.debug(f"开始状态同步 (尝试 {attempt + 1}/3)...")
                
                positions_f = self.exchange.fetch_positions([self.symbol])
                orders_f = self.exchange.fetch_open_orders(self.symbol)
                # [修改] 使用 fetch_bids_asks 获取更可靠的买卖价
                bids_asks_f = self.exchange.fetch_bids_asks([self.symbol])
                
                positions, open_orders, bids_asks = await asyncio.gather(positions_f, orders_f, bids_asks_f, return_exceptions=True)
                
                if isinstance(positions, Exception): raise positions
                if isinstance(open_orders, Exception): raise open_orders
                if isinstance(bids_asks, Exception): raise bids_asks

                # [修改] 增加对bids_asks数据的健壮性检查
                if not bids_asks or self.symbol not in bids_asks:
                    logger.warning(f"获取的Bids/Asks数据结构不完整: {bids_asks}，准备重试...")
                    await asyncio.sleep(1) # 等待1秒后重试
                    continue
                
                ticker = bids_asks[self.symbol]
                if not ticker or ticker.get('bid') is None or ticker.get('ask') is None:
                    logger.warning(f"获取的Ticker数据内容不完整: {ticker}，准备重试...")
                    await asyncio.sleep(1) # 等待1秒后重试
                    continue

                self.best_bid_price = float(ticker['bid'])
                self.best_ask_price = float(ticker['ask'])
                self.last_price = (self.best_bid_price + self.best_ask_price) / 2
                
                long_pos = next((p for p in positions if p.get('info', {}).get('positionSide') == 'LONG'), None)
                short_pos = next((p for p in positions if p.get('info', {}).get('positionSide') == 'SHORT'), None)
                self.long_position_size = float(long_pos.get('contracts', 0)) if long_pos else 0.0
                self.short_position_size = float(short_pos.get('contracts', 0)) if short_pos else 0.0

                valid_orders = {o['clientOrderId']: o for o in open_orders if o.get('clientOrderId')}
                self.open_orders = valid_orders
                
                self.last_state_sync_time = time.time()
                logger.debug(f"状态同步完成。持仓: L={self.long_position_size:.4f}, S={self.short_position_size:.4f}. 挂单: {len(self.open_orders)}")
                return # [新增] 成功后直接返回

            except Exception as e:
                logger.error(f"状态同步失败 (尝试 {attempt + 1}/3): {e}", exc_info=True)
                if attempt < 2:
                    await asyncio.sleep(1) # 等待后重试
                else:
                    logger.critical("所有状态同步尝试均失败，将引发错误。")
                    raise
        raise Exception("状态同步在多次重试后彻底失败")

    async def full_state_sync(self):
        """通过REST API完全同步持仓和挂单状态"""
        async with self.lock:
            await self._internal_state_sync()

    # --------------------------------------------------------------------------
    # --- [新增] 全局硬止损风控 ---
    # --------------------------------------------------------------------------
    async def monitor_account_drawdown(self):
        """后台任务，定期监控账户总权益回撤"""
        risk_cfg = self.config['risk_management']
        interval = risk_cfg['drawdown_check_interval_seconds']
        
        while not self.is_shutting_down and not self.is_permanently_stopped:
            try:
                await asyncio.sleep(interval)
                
                if self.is_permanently_stopped: break

                balance = await self.exchange.fetch_balance()
                current_equity = float(balance.get('info', {}).get('totalWalletBalance', 0))
                if current_equity <= 0:
                    continue
                
                self.peak_equity = max(self.peak_equity, current_equity)
                drawdown = (self.peak_equity - current_equity) / self.peak_equity if self.peak_equity > 0 else 0
                
                logger.info(f"权益更新: 当前={current_equity:.2f}, 峰值={self.peak_equity:.2f}, 回撤={drawdown:.2%}")

                if drawdown >= risk_cfg['max_account_drawdown_pct']:
                    logger.critical(f"风控警报：账户回撤达到 {drawdown:.2%}")
                    await self.trigger_emergency_stop()
                    break

            except Exception as e:
                logger.error(f"监控账户回撤时出错: {e}", exc_info=True)

    async def trigger_emergency_stop(self):
        """执行紧急止损：清仓、撤单、停机"""
        if self.is_permanently_stopped: return
        self.is_permanently_stopped = True
        logger.info("--- 紧急止损流程开始 ---")
        await self.cancel_all_open_orders()
        await self.close_all_positions()
        logger.critical("所有仓位已平，所有挂单已撤销。策略已永久停止。")

    async def close_all_positions(self):
        """市价平掉所有仓位"""
        try:
            positions = await self.exchange.fetch_positions([self.symbol])
            positions_to_close = [p for p in positions if p.get('contracts') and float(p['contracts']) != 0]
            if not positions_to_close:
                logger.info("当前无持仓，无需执行平仓操作")
                return

            for position in positions_to_close:
                side = 'sell' if position['side'] == 'long' else 'buy'
                amount = float(position['contracts'])
                params = {'reduceOnly': True}
                logger.warning(f"准备提交市价平仓单: {side} {amount} {self.symbol}")
                await self.exchange.create_order(self.symbol, 'market', side, amount, params=params)
        except Exception as e:
            logger.error(f"市价平仓时发生严重错误: {e}", exc_info=True)
            raise

    # --------------------------------------------------------------------------
    # --- 辅助函数 ---
    # --------------------------------------------------------------------------
    async def update_price_series(self):
        """更新用于计算指标的K线数据"""
        try:
            cfg = self.config['strategy_config']['adaptive_grid']
            limit = cfg['ema_trend_period'] + 10
            timeframe = cfg.get('kline_timeframe', '1m')
            
            ohlcv = await asyncio.wait_for(
                self.exchange.fetch_ohlcv(self.symbol, timeframe, limit=limit),
                timeout=30
            )
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            if df.isnull().values.any():
                df = df.dropna()
            self.price_series = df
        except Exception as e:
            logger.error(f"更新K线数据失败: {e}", exc_info=True)

    def generate_arithmetic_grids(self, high, low, count):
        """[新增] 算术（等差）网格生成"""
        if high <= low or count < 2:
            logger.error("算术网格参数无效")
            return []
        
        step = (high - low) / (count - 1)
        grids = [low + i * step for i in range(count)]
        try:
            price_precision = self.market['precision']['price']
            decimals = int(-math.log10(price_precision)) if price_precision > 0 else 4
            rounded_grids = np.round(grids, decimals)
            return sorted(list(set(rounded_grids)))
        except Exception as e:
            logger.error(f"算术网格精度处理失败: {e}")
            return grids

    def generate_linear_grids(self, center, low, high, spacing_pct):
        """线性等比间距网格生成"""
        if high <= low or spacing_pct <= 0: return []
        grids = []
        
        current_price = center
        while (current_price := current_price * (1 + spacing_pct)) <= high:
            grids.append(current_price)
        
        current_price = center
        while (current_price := current_price * (1 - spacing_pct)) >= low:
            grids.append(current_price)
        
        try:
            price_precision = self.market['precision']['price']
            decimals = int(-math.log10(price_precision)) if price_precision > 0 else 4
            rounded_grids = np.round(grids, decimals)
            return sorted(list(set(rounded_grids)))
        except Exception as e:
            logger.error(f"线性网格精度处理失败: {e}")
            return grids
    
    def calculate_grid_overlap(self, new_grids, old_grids):
        """计算新旧网格重合度"""
        if not old_grids or not new_grids: return 0.0
        old_set = set(old_grids)
        new_set = set(new_grids)
        return len(old_set.intersection(new_set)) / len(old_set)

    async def set_hedge_mode(self):
        """设置账户为双向持仓模式"""
        try:
            await self.exchange.fapiPrivatePostPositionSideDual({"dualSidePosition": "true"})
            logger.info("双向持仓模式已成功设置")
        except Exception as e:
            if "No need to change" in str(e):
                logger.info("当前已是双向持仓模式")
            else:
                logger.error(f"设置双向持仓模式失败: {e}", exc_info=True)

    async def set_leverage(self):
        """设置杠杆倍数"""
        try:
            leverage = self.config['leverage']
            await self.exchange.set_leverage(leverage, self.symbol)
            logger.info(f"杠杆已设置为 {leverage}x")
        except Exception as e:
            if "leverage not modified" in str(e):
                logger.info(f"杠杆已经是 {self.config['leverage']}x")
            else:
                logger.error(f"设置杠杆失败: {e}", exc_info=True)

    async def fetch_listen_key(self):
        """获取WebSocket监听密钥"""
        try:
            response = await self.exchange.fapiPrivatePostListenKey({})
            return response['listenKey']
        except Exception as e:
            logger.error(f"获取Listen Key失败: {e}", exc_info=True)
            raise

    async def keep_listen_key_alive(self):
        """后台任务，定期延长 listenKey 的有效期"""
        while not self.is_shutting_down:
            await asyncio.sleep(1800)  # 每30分钟
            try:
                await self.exchange.fapiPrivatePutListenKey({})
                logger.debug("Listen Key有效期已延长")
            except Exception as e:
                logger.error(f"延长Listen Key失败: {e}", exc_info=True)


# ====================================================================================
# --- 主程序入口 ---
# ====================================================================================
async def main():
    """主程序入口函数"""
    bot = None
    try:
        logger.info("启动 ARGM-V6.1 策略...")
        bot = ARGMStrategyBot(CONFIG)
        await bot.run()
    except KeyboardInterrupt:
        logger.info("检测到用户中断 (Ctrl+C)...")
    except Exception as e:
        logger.critical(f"程序顶层发生未捕获的严重错误: {e}", exc_info=True)
    finally:
        if bot:
            await bot.close()
    logger.info("程序已完全退出。")

if __name__ == "__main__":
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())